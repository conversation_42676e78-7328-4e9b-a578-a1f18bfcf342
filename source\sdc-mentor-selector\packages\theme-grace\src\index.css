/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.15;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}
/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers.
 */
body {
  margin: 0;
}
/**
 * Render the `main` element consistently in IE.
 */
main {
  display: block;
}
/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
/* Grouping content
   ========================================================================== */
/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */
}
/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}
/* Text-level semantics
   ========================================================================== */
/**
 * Remove the gray background on active links in IE 10.
 */
a {
  background-color: transparent;
}
/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */
  text-decoration: underline dotted;
  /* 2 */
}
/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}
/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}
/**
 * Add the correct font size in all browsers.
 */
small {
  font-size: 80%;
}
/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10.
 */
img {
  border-style: none;
}
/* Forms
   ========================================================================== */
/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: 1.15;
  /* 1 */
  margin: 0;
  /* 2 */
}
/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
  /* 1 */
  overflow: visible;
}
/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  /* 1 */
  text-transform: none;
}
/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}
/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}
/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}
/**
 * Correct the padding in Firefox.
 */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}
/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box;
  /* 1 */
  color: inherit;
  /* 2 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  white-space: normal;
  /* 1 */
}
/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline;
}
/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
  overflow: auto;
}
/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type="checkbox"],
[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}
/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}
/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}
/* Interactive
   ========================================================================== */
/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
  display: block;
}
/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item;
}
/* Misc
   ========================================================================== */
/**
 * Add the correct display in IE 10+.
 */
template {
  display: none;
}
/**
 * Add the correct display in IE 10.
 */
[hidden] {
  display: none;
}
/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #f5f7f9;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(144, 147, 153, 0.3);
  border-radius: 10px;
  background-color: #f5f7f9;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(144, 147, 153, 0.3);
  background-color: rgba(144, 147, 153, 0.3);
  transition: background-color 0.3s;
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(144, 147, 153, 0.5);
}
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /*点击元素清除默认高亮*/
  -webkit-tap-highlight-color: transparent;
}
html,
body,
#app {
  height: 100%;
}
a {
  color: #666;
  text-decoration: none;
}
a:focus,
a:hover,
a:visited {
  outline: none;
}
img {
  border: none;
  vertical-align: middle;
}
a,
img {
  /*清除默认链接，图片长按的下拉菜单*/
  -webkit-touch-callout: none;
}
input,
textarea {
  outline: none;
  /*清除默认在ios上的文本框外观*/
  -webkit-appearance: none;
}
textarea {
  resize: none;
}
ul {
  list-style: none;
}
video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  outline: none;
}
/*.clear-fix::after {*/
/*  clear: both;*/
/*  content: '';*/
/*  display: block;*/
/*  width: 0;*/
/*  height: 0;*/
/*  visibility: hidden;*/
/*}*/
.clear-fix::before,
.clear-fix::after {
  content: "";
  display: table;
}
.clear-fix::after {
  clear: both;
}
.clear-fix {
  *zoom: 1;
}
.left {
  float: left;
}
.right {
  float: right;
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.brick-item {
  cursor: pointer;
  transition: all 0.2s linear;
}
.brick-item:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  transform: translate3d(0, -2px, 0);
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 9999;
  filter: alpha(opacity=65);
  -moz-opacity: 0.65;
  opacity: 0.65;
}
.fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  border-top: none;
  background-color: rgba(244, 249, 255, 0.9);
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.1);
  z-index: 9999;
}
.fixed-bottom {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  border: none;
  background-color: rgba(244, 249, 255, 0.9);
  box-shadow: 5px 0 5px rgba(0, 0, 0, 0.1);
  z-index: 9999;
}
.el-tooltip__popper.is-light {
  border: 0 !important;
  box-shadow: 0 0 7px 0 rgba(171, 171, 171, 0.4);
}
.el-tooltip__popper.is-light[x-placement^=top] .popper__arrow {
  border-top-color: #f5f7f9 !important;
}
.el-tooltip__popper.is-light[x-placement^=bottom] .popper__arrow {
  border-bottom-color: #f5f7f9 !important;
}
.el-tooltip__popper.is-light[x-placement^=left] .popper__arrow {
  border-left-color: #f5f7f9 !important;
}
.el-tooltip__popper.is-light[x-placement^=right] .popper__arrow {
  border-right-color: #f5f7f9 !important;
}
.el-input__inner,
.el-textarea__inner {
  border-radius: 4px;
}
.el-switch.is-disabled.is-checked {
  opacity: 1;
}
.el-switch.is-disabled.is-checked span {
  border-color: #CED9F8;
  background-color: #CED9F8;
}
button.el-button {
  font-weight: normal;
}
.sdc-container {
  display: flex;
  flex-direction: row;
  flex: 1;
  flex-basis: auto;
  box-sizing: border-box;
  min-width: 0;
  height: 100%;
}
.sdc-container.vertical {
  flex-direction: column;
}
.sdc-nav-menu .el-menu {
  background-color: rgba(0, 0, 0, 0);
}
.sdc-nav-menu .el-menu.el-menu--horizontal {
  border-bottom: none;
}
.sdc-nav-menu .el-menu .el-submenu .el-submenu__title,
.sdc-nav-menu .el-menu .el-submenu.is-active .el-submenu__title {
  border-bottom: none;
  background-color: rgba(0, 0, 0, 0) !important;
  color: #fff !important;
  margin-left: -20px;
}
.sdc-nav-menu .el-menu .el-submenu .el-submenu__title [class*="el-icon-"],
.sdc-nav-menu .el-menu .el-submenu.is-active .el-submenu__title [class*="el-icon-"] {
  color: #fff;
}
.sdc-nav-menu .el-menu .el-submenu .el-submenu__title .el-submenu__icon-arrow,
.sdc-nav-menu .el-menu .el-submenu.is-active .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}
.sdc-drop-menu .el-dropdown-menu__item {
  padding: 0;
}
.sdc-drop-menu .el-dropdown-menu__item .nav-item {
  width: 100%;
  padding: 0 20px;
}
.sdc-nav-menu-popper .el-menu {
  padding: 10px 0;
}
.sdc-nav-menu-popper .el-menu .el-menu-item,
.sdc-nav-menu-popper .el-menu .el-submenu__title {
  position: relative;
  padding-left: 55px;
  color: #333;
}
.sdc-nav-menu-popper .el-menu .el-menu-item i,
.sdc-nav-menu-popper .el-menu .el-submenu__title i {
  color: #333;
}
.sdc-nav-menu-popper .el-menu .el-menu-item i:first-child:not(.el-submenu__icon-arrow),
.sdc-nav-menu-popper .el-menu .el-submenu__title i:first-child:not(.el-submenu__icon-arrow) {
  position: absolute;
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
}
.sdc-nav-menu-popper .el-menu .is-opened > .el-submenu__title,
.sdc-nav-menu-popper .el-menu .el-menu-item:hover,
.sdc-nav-menu-popper .el-menu .el-submenu__title:hover {
  color: #3464e0 !important;
  background-color: #ebf0fc;
}
[data-theme='skyblue'] .sdc-nav-menu-popper .el-menu .is-opened > .el-submenu__title,
[data-theme='skyblue'] .sdc-nav-menu-popper .el-menu .el-menu-item:hover,
[data-theme='skyblue'] .sdc-nav-menu-popper .el-menu .el-submenu__title:hover {
  color: #3464e0 !important;
}
[data-theme='grassgreen'] .sdc-nav-menu-popper .el-menu .is-opened > .el-submenu__title,
[data-theme='grassgreen'] .sdc-nav-menu-popper .el-menu .el-menu-item:hover,
[data-theme='grassgreen'] .sdc-nav-menu-popper .el-menu .el-submenu__title:hover {
  color: #0ad0b6 !important;
}
.sdc-nav-menu-popper .el-menu .is-opened > .el-submenu__title [class*="el-icon-"],
.sdc-nav-menu-popper .el-menu .el-menu-item:hover [class*="el-icon-"],
.sdc-nav-menu-popper .el-menu .el-submenu__title:hover [class*="el-icon-"] {
  color: #3464e0;
}
[data-theme='skyblue'] .sdc-nav-menu-popper .el-menu .is-opened > .el-submenu__title [class*="el-icon-"],
[data-theme='skyblue'] .sdc-nav-menu-popper .el-menu .el-menu-item:hover [class*="el-icon-"],
[data-theme='skyblue'] .sdc-nav-menu-popper .el-menu .el-submenu__title:hover [class*="el-icon-"] {
  color: #3464e0;
}
[data-theme='grassgreen'] .sdc-nav-menu-popper .el-menu .is-opened > .el-submenu__title [class*="el-icon-"],
[data-theme='grassgreen'] .sdc-nav-menu-popper .el-menu .el-menu-item:hover [class*="el-icon-"],
[data-theme='grassgreen'] .sdc-nav-menu-popper .el-menu .el-submenu__title:hover [class*="el-icon-"] {
  color: #0ad0b6;
}
.sdc-header {
  width: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
  color: #fff;
  height: 60px;
  line-height: 60px;
  font-size: 14px;
  background-color: #1890ff;
  background: linear-gradient(to right, #3464e0, #1890ff);
}
[data-theme='skyblue'] .sdc-header {
  background-color: #1890ff;
}
[data-theme='grassgreen'] .sdc-header {
  background-color: #4caf50;
}
[data-theme='skyblue'] .sdc-header {
  background: linear-gradient(to right, #3464e0, #1890ff);
}
[data-theme='grassgreen'] .sdc-header {
  background: linear-gradient(to right, #009688, #4caf50);
}
.sdc-header.sdc-header-oc .header-inner {
  background: url(../img/logo-bg.png) no-repeat;
  background-position-x: 140px;
}
.sdc-header .header-inner {
  display: flex;
  flex: 1;
  justify-content: space-between;
}
.sdc-header .header-inner .header-left {
  display: flex;
  justify-content: space-between;
  width: 210px;
  padding-left: 15px;
}
.sdc-header .header-inner .header-left.has-nav {
  width: 300px;
  padding-left: 20px;
  margin-left: 0;
}
.sdc-header .header-inner .header-left .nav {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  cursor: pointer;
}
.sdc-header .header-inner .header-left .nav i {
  margin-right: 5px;
  font-size: 18px;
}
.sdc-header .header-inner .header-left .nav:after {
  position: absolute;
  content: '';
  top: 24px;
  right: 5px;
  width: 1px;
  height: 12px;
  border-left: 1px solid #fff;
  border-left: 1px solid rgba(255, 255, 255, 0.3);
}
.sdc-header .header-inner .header-left .logo {
  flex: 3;
  width: 100%;
  height: 60px;
  cursor: pointer;
}
.sdc-header .header-inner .header-left .logo .logo-icon {
  display: inline-block;
  width: 100%;
  height: 100%;
  margin-left: -20px;
}
.sdc-header .header-inner .header-left .logo .logo-icon.logo-oa {
  background: url(../img/logo-oa.svg) no-repeat center;
}
.sdc-header .header-inner .header-left .logo .logo-icon.logo-oc {
  background: url(../img/logo-oc.svg) no-repeat center;
}
.sdc-header .header-inner .header-left .logo .logo-text {
  display: inline-block;
  padding-left: 10px;
  font-size: 16px;
  color: #fff;
  vertical-align: middle;
}
.sdc-header .header-inner .header-center {
  flex: 1;
  margin-right: 40px;
}
.sdc-header .header-inner .header-center .sdc-navbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
}
.sdc-header .header-inner .header-center .sdc-navbar li {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 120px;
  padding: 0 10px;
  margin-right: 10px;
  cursor: pointer;
}
.sdc-header .header-inner .header-center .sdc-navbar li:last-child {
  margin-right: 0;
}
.sdc-header .header-inner .header-center .sdc-navbar li .el-dropdown {
  outline: none;
  color: #fff;
  width: 100%;
  text-align: center;
}
.sdc-header .header-inner .header-center .sdc-navbar li .nav-item {
  position: relative;
  color: #fff;
  outline: none;
}
.sdc-header .header-inner .header-center .sdc-navbar li .nav-item.selected,
.sdc-header .header-inner .header-center .sdc-navbar li .nav-item:hover {
  font-weight: 600;
  font-size: 16px;
}
.sdc-header .header-inner .header-center .sdc-navbar li .nav-item.selected:after,
.sdc-header .header-inner .header-center .sdc-navbar li .nav-item:hover:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 16px;
  width: 20px;
  height: 3px;
  border-radius: 3px;
  background-color: #fff;
}
.sdc-header .header-inner .header-right {
  display: flex;
  padding-right: 30px;
  justify-content: flex-end;
}
.sdc-header .header-inner .header-right .header-right-inner {
  display: flex;
  align-items: center;
}
.sdc-header .header-inner .header-right .sdc-search {
  width: 300px;
  padding-right: 40px;
}
.sdc-header .header-inner .header-right .sdc-search .el-input__inner {
  height: 36px;
  line-height: 36px;
  border: none;
  font-size: 12px;
  border-radius: 6px;
}
.sdc-header .header-inner .header-right .sdc-search .el-input__inner + .el-input__suffix {
  cursor: pointer;
}
.sdc-header .header-inner .header-right a {
  display: inline-block;
  color: #fff;
  margin-right: 15px;
}
.sdc-header .header-inner .header-right a > i {
  font-size: 20px;
  vertical-align: middle;
}
.sdc-header .header-inner .header-right a:last-child {
  margin-right: 0;
}
.sdc-header .menu-icon {
  width: 18px;
  height: 18px;
  background: url('../img/nav.svg') no-repeat center;
  display: inline-block;
  margin-right: 5px;
}
.sdc-drop-menu {
  min-width: 100px;
}
.sdc-drop-menu.app-drop-menu .el-dropdown-menu__item {
  height: 36px;
}
.sdc-drop-menu .el-dropdown-menu__item a {
  display: inline-block;
  max-width: 200px !important;
}
.sdc-drop-menu .el-dropdown-menu__item:hover,
.sdc-drop-menu .el-dropdown-menu__item:hover a {
  color: #3464e0;
  background-color: #f5f7f9;
}
[data-theme='skyblue'] .sdc-drop-menu .el-dropdown-menu__item:hover,
[data-theme='skyblue'] .sdc-drop-menu .el-dropdown-menu__item:hover a {
  color: #3464e0;
}
[data-theme='grassgreen'] .sdc-drop-menu .el-dropdown-menu__item:hover,
[data-theme='grassgreen'] .sdc-drop-menu .el-dropdown-menu__item:hover a {
  color: #0ad0b6;
}
.sdc-drop-menu .el-dropdown-menu__item i {
  width: 15px;
  text-align: center;
}
.sdc-sidebar {
  position: absolute;
  width: 210px;
  height: 100%;
  left: 0;
  background-color: #fff;
}
.sdc-sidebar.sidebar-collapse {
  width: 50px;
}
.sdc-sidebar .el-menu {
  border-right: none;
  overflow-x: hidden;
  overflow-y: auto;
}
.sdc-sidebar .el-menu.el-menu--inline {
  padding-top: 0;
  overflow-y: hidden;
}
.sdc-sidebar .el-menu.el-menu--inline .el-submenu__title,
.sdc-sidebar .el-menu.el-menu--inline .el-menu-item {
  color: #666;
}
.sdc-sidebar .el-menu .el-tooltip {
  outline: none;
}
.sdc-sidebar .el-menu.el-menu--collapse {
  width: 50px;
}
.sdc-sidebar .el-menu.el-menu--collapse .el-tooltip {
  position: absolute;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 100%;
  display: inline-block;
  box-sizing: border-box;
  text-align: center;
}
.sdc-sidebar .el-menu.el-menu--collapse .el-submenu__title i.menu-icon,
.sdc-sidebar .el-menu.el-menu--collapse .el-menu-item i.menu-icon {
  margin: 0;
}
.sdc-sidebar .el-menu .el-submenu__title,
.sdc-sidebar .el-menu .el-menu-item {
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.sdc-sidebar .el-menu .el-submenu__title i.menu-icon,
.sdc-sidebar .el-menu .el-menu-item i.menu-icon {
  margin-right: 10px;
  vertical-align: middle;
  width: 20px;
  font-size: 20px;
  text-align: center;
  color: #333;
}
.sdc-sidebar .el-menu .el-submenu__title.disabled,
.sdc-sidebar .el-menu .el-menu-item.disabled {
  cursor: not-allowed;
}
.sdc-sidebar .el-menu .el-submenu__title.is-active,
.sdc-sidebar .el-menu .el-menu-item.is-active,
.sdc-sidebar .el-menu .el-submenu__title:hover:not(.disabled),
.sdc-sidebar .el-menu .el-menu-item:hover:not(.disabled) {
  color: #3464e0 !important;
  background-color: #f5f7f9;
}
[data-theme='skyblue'] .sdc-sidebar .el-menu .el-submenu__title.is-active,
[data-theme='skyblue'] .sdc-sidebar .el-menu .el-menu-item.is-active,
[data-theme='skyblue'] .sdc-sidebar .el-menu .el-submenu__title:hover:not(.disabled),
[data-theme='skyblue'] .sdc-sidebar .el-menu .el-menu-item:hover:not(.disabled) {
  color: #3464e0 !important;
}
[data-theme='grassgreen'] .sdc-sidebar .el-menu .el-submenu__title.is-active,
[data-theme='grassgreen'] .sdc-sidebar .el-menu .el-menu-item.is-active,
[data-theme='grassgreen'] .sdc-sidebar .el-menu .el-submenu__title:hover:not(.disabled),
[data-theme='grassgreen'] .sdc-sidebar .el-menu .el-menu-item:hover:not(.disabled) {
  color: #0ad0b6 !important;
}
.sdc-sidebar .el-menu .el-submenu__title.is-active i,
.sdc-sidebar .el-menu .el-menu-item.is-active i,
.sdc-sidebar .el-menu .el-submenu__title:hover:not(.disabled) i,
.sdc-sidebar .el-menu .el-menu-item:hover:not(.disabled) i {
  color: #3464e0;
}
[data-theme='skyblue'] .sdc-sidebar .el-menu .el-submenu__title.is-active i,
[data-theme='skyblue'] .sdc-sidebar .el-menu .el-menu-item.is-active i,
[data-theme='skyblue'] .sdc-sidebar .el-menu .el-submenu__title:hover:not(.disabled) i,
[data-theme='skyblue'] .sdc-sidebar .el-menu .el-menu-item:hover:not(.disabled) i {
  color: #3464e0;
}
[data-theme='grassgreen'] .sdc-sidebar .el-menu .el-submenu__title.is-active i,
[data-theme='grassgreen'] .sdc-sidebar .el-menu .el-menu-item.is-active i,
[data-theme='grassgreen'] .sdc-sidebar .el-menu .el-submenu__title:hover:not(.disabled) i,
[data-theme='grassgreen'] .sdc-sidebar .el-menu .el-menu-item:hover:not(.disabled) i {
  color: #0ad0b6;
}
.sdc-sidebar .el-menu .el-submenu__title .badge,
.sdc-sidebar .el-menu .el-menu-item .badge {
  margin-left: 5px;
  background-color: #f81d22;
  border-radius: 10px;
  color: #fff;
  display: inline-block;
  font-size: 12px;
  height: 18px;
  line-height: 18px;
  padding: 0 6px;
  text-align: center;
  white-space: nowrap;
  box-sizing: content-box;
}
.sdc-sidebar .toggle-sidebar {
  position: absolute;
  bottom: 10px;
  padding: 0 10px;
  width: 210px;
  height: 40px;
  line-height: 40px;
  background-color: #fff;
  z-index: 999;
  text-align: right;
}
.sdc-sidebar .toggle-sidebar .toggle-icon {
  display: inline-block;
  margin-top: 10px;
  width: 20px;
  height: 20px;
  background: url(../img/fold-gray.svg) no-repeat center;
  cursor: pointer;
}
.sdc-sidebar .toggle-sidebar .toggle-icon:hover {
  background-image: url(../img/fold-blue.svg);
}
.sdc-sidebar .toggle-sidebar .toggle-icon.unfold {
  transform: rotate(180deg);
}
.sdc-sidebar .toggle-sidebar.toggle-collapse {
  width: 50px;
  text-align: center;
}
.sdc-sidebar .sdc-menu-item-2.el-menu-item,
.sdc-sidebar .sdc-menu-item-2 > .el-submenu__title {
  padding-left: 50px !important;
}
.sdc-sidebar .sdc-menu-item-3.el-menu-item,
.sdc-sidebar .sdc-menu-item-3 > .el-submenu__title {
  padding-left: 80px !important;
}
.sdc-sidebar .sdc-menu-item-4.el-menu-item,
.sdc-sidebar .sdc-menu-item-4 > .el-submenu__title {
  padding-left: 110px !important;
}
.sdc-sidebar .sdc-menu-item-5.el-menu-item,
.sdc-sidebar .sdc-menu-item-5 > .el-submenu__title {
  padding-left: 140px !important;
}
.sdc-sidebar-menu-popper {
  margin-left: 0;
}
.sdc-sidebar-menu-popper .el-menu--popup {
  border-radius: 6px;
  padding: 10px 0;
}
.sdc-sidebar-menu-popper .el-menu-item,
.sdc-sidebar-menu-popper .el-submenu__title {
  height: 40px !important;
  line-height: 40px !important;
  font-size: 14px;
  color: #666;
}
.sdc-sidebar-menu-popper .el-menu-item:hover:not(.disabled),
.sdc-sidebar-menu-popper .el-submenu__title:hover:not(.disabled),
.sdc-sidebar-menu-popper .el-menu-item.is-active,
.sdc-sidebar-menu-popper .el-submenu__title.is-active {
  color: #3464e0 !important;
  background-color: #f5f7f9;
}
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-menu-item:hover:not(.disabled),
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-submenu__title:hover:not(.disabled),
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-menu-item.is-active,
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-submenu__title.is-active {
  color: #3464e0 !important;
}
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-menu-item:hover:not(.disabled),
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-submenu__title:hover:not(.disabled),
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-menu-item.is-active,
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-submenu__title.is-active {
  color: #0ad0b6 !important;
}
.sdc-sidebar-menu-popper .el-menu-item:hover:not(.disabled) i,
.sdc-sidebar-menu-popper .el-submenu__title:hover:not(.disabled) i,
.sdc-sidebar-menu-popper .el-menu-item.is-active i,
.sdc-sidebar-menu-popper .el-submenu__title.is-active i,
.sdc-sidebar-menu-popper .el-menu-item:hover:not(.disabled) i.menu-icon,
.sdc-sidebar-menu-popper .el-submenu__title:hover:not(.disabled) i.menu-icon,
.sdc-sidebar-menu-popper .el-menu-item.is-active i.menu-icon,
.sdc-sidebar-menu-popper .el-submenu__title.is-active i.menu-icon {
  color: #3464e0;
}
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-menu-item:hover:not(.disabled) i,
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-submenu__title:hover:not(.disabled) i,
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-menu-item.is-active i,
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-submenu__title.is-active i,
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-menu-item:hover:not(.disabled) i.menu-icon,
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-submenu__title:hover:not(.disabled) i.menu-icon,
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-menu-item.is-active i.menu-icon,
[data-theme='skyblue'] .sdc-sidebar-menu-popper .el-submenu__title.is-active i.menu-icon {
  color: #3464e0;
}
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-menu-item:hover:not(.disabled) i,
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-submenu__title:hover:not(.disabled) i,
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-menu-item.is-active i,
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-submenu__title.is-active i,
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-menu-item:hover:not(.disabled) i.menu-icon,
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-submenu__title:hover:not(.disabled) i.menu-icon,
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-menu-item.is-active i.menu-icon,
[data-theme='grassgreen'] .sdc-sidebar-menu-popper .el-submenu__title.is-active i.menu-icon {
  color: #0ad0b6;
}
.sdc-sidebar-menu-popper .el-menu-item i.menu-icon,
.sdc-sidebar-menu-popper .el-submenu__title i.menu-icon {
  margin-right: 10px;
  vertical-align: middle;
  width: 20px;
  font-size: 20px;
  text-align: center;
  color: #666;
}
.sdc-sidebar-menu-popper .badge {
  margin-left: 5px;
  background-color: #f81d22;
  border-radius: 10px;
  color: #fff;
  display: inline-block;
  font-size: 12px;
  height: 18px;
  line-height: 18px;
  padding: 0 6px;
  text-align: center;
  white-space: nowrap;
  box-sizing: content-box;
}
.sdc-sidebar-menu-tooltip-popper {
  font-size: 14px;
}
.sdc-sidebar-menu-tooltip-popper.is-dark {
  background-color: #333;
  opacity: 0.9;
}
.sdc-content {
  position: relative;
  display: flex;
  flex: 1;
  flex-basis: auto;
  overflow: auto;
  box-sizing: border-box;
  padding: 0;
}
.sdc-content .page-nav {
  position: relative;
  padding-top: 10px;
  width: 210px;
  background-color: #fff;
  transition: all 0.5s linear;
  box-shadow: 0 0 7px 0 rgba(171, 171, 171, 0.2);
  overflow: hidden;
}
.sdc-content .page-nav.nav-collapse {
  width: 50px;
}
.sdc-content .page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f0f4f9;
}
.sdc-content .page-content .el-breadcrumb {
  position: relative;
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  background: #fff;
  border-bottom: 1px solid #f5f7f9;
}
.sdc-content .page-content .sdc-router-view {
  height: 100%;
  width: 100%;
  overflow: auto;
  padding: 10px;
}
.sdc-content .sdc-back-top {
  bottom: 20px !important;
  color: #3464e0;
}
.sdc-content .sdc-back-top i {
  outline: none;
}
[data-theme='skyblue'] .sdc-content .sdc-back-top {
  color: #3464e0;
}
[data-theme='grassgreen'] .sdc-content .sdc-back-top {
  color: #0ad0b6;
}
.sdc-backtop-popper {
  border: 1px solid #dcdcdc !important;
}
.sdc-backtop-popper .popper__arrow {
  border-top-color: #dcdcdc !important;
}
@-webkit-keyframes spinner {
  50% {
    transform: scale(0.4);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.sdc-loading .mask {
  display: flex;
  justify-content: center;
  align-items: center;
}
.sdc-loading .mask .text {
  margin-top: 10px;
  font-size: 13px;
  color: #fff;
}
.sdc-loading .mask .spinner {
  position: absolute;
  transform: translateY(-50px);
}
.sdc-loading .mask .spinner i {
  position: absolute;
  display: block;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: #1890ff;
}
.sdc-loading .mask .spinner i:nth-child(1) {
  top: 25px;
  left: 0;
  -webkit-animation: spinner 1s ease 0s infinite;
}
.sdc-loading .mask .spinner i:nth-child(2) {
  top: 17px;
  left: 17px;
  -webkit-animation: spinner 1s ease -0.12s infinite;
}
.sdc-loading .mask .spinner i:nth-child(3) {
  top: 0;
  left: 25px;
  -webkit-animation: spinner 1s ease -0.24s infinite;
}
.sdc-loading .mask .spinner i:nth-child(4) {
  top: -17px;
  left: 17px;
  -webkit-animation: spinner 1s ease -0.36s infinite;
}
.sdc-loading .mask .spinner i:nth-child(5) {
  top: -25px;
  left: 0;
  -webkit-animation: spinner 1s ease -0.48s infinite;
}
.sdc-loading .mask .spinner i:nth-child(6) {
  top: -17px;
  left: -17px;
  -webkit-animation: spinner 1s ease -0.6s infinite;
}
.sdc-loading .mask .spinner i:nth-child(7) {
  top: 0;
  left: -25px;
  -webkit-animation: spinner 1s ease -0.72s infinite;
}
.sdc-loading .mask .spinner i:nth-child(8) {
  top: 17px;
  left: -17px;
  -webkit-animation: spinner 1s ease -0.84s infinite;
}
.sdc-loading .fade-enter-active,
.sdc-loading .fade-leave-active {
  transition: opacity 0.2s;
}
.sdc-loading .fade-enter,
.sdc-loading .fade-leave-active {
  opacity: 0;
}
.sdc-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 8px 10px;
  z-index: 9999;
  font-size: 14px;
  color: #fff;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.75);
  opacity: 1;
}
.sdc-toast .fade-enter-active,
.sdc-toast .fade-leave-active {
  transition: opacity 0.2s;
}
.sdc-toast .fade-enter,
.sdc-toast .fade-leave-active {
  opacity: 0;
}
.sdc-table td.el-table-column--selection .cell {
  padding-left: 10px !important;
}
.sdc-table th .cell .selection-item {
  display: block;
  cursor: pointer;
  font-size: 16px;
}
.sdc-table tr.el-table__row .el-radio.selection .el-radio__label {
  padding-left: 0;
}
.sdc-table .action-cell {
  display: flex;
}
.sdc-table .table-header {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.sdc-table .table-header /deep/ .el-button {
  min-width: 80px;
  padding: 10px 15px;
  font-size: 12px;
  border-radius: 3px;
}
.sdc-table .table-header > .left {
  color: #333;
  font-size: 16px;
  font-weight: 600;
}
.sdc-table .table-header > .right {
  display: flex;
  align-items: center;
}
.sdc-table .table-header > .right .list {
  margin-left: 10px;
}
.sdc-table .table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sdc-table .table-footer .footer {
  flex: 2;
}
.sdc-table .table-footer .sdc-pager {
  display: flex;
  flex: 4;
  justify-content: flex-end;
  margin-top: 10px;
}
.sdc-table .el-table__header th {
  color: #333;
  background-color: #F5F7F9;
}
.sdc-selector {
  display: flex;
}
.sdc-selector--textarea {
  height: 150px;
  flex-direction: column-reverse;
}
.sdc-selector--normal {
  align-items: center;
}
.sdc-selector .textarea-bar {
  margin-bottom: 10px;
  font-size: 14px;
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
  color: #666;
}
.sdc-selector .textarea-bar .num {
  font-size: 14px;
  color: #999;
}
.sdc-selector .textarea-bar .el-button--mini {
  font-size: 14px;
  height: 30px;
  padding: 6px 15px;
}
.sdc-selector .selector-container {
  display: inline-block;
  font-size: 14px;
  flex: 1;
  height: 40px;
  border: 1px solid #dcdcdc;
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
}
.sdc-selector .selector-container.selector-container--normal {
  width: 0;
}
.sdc-selector .selector-container.is-disabled {
  background-color: #f2f2f2;
}
.sdc-selector .selector-container--focus {
  border-right: 1px solid #3464e0 !important;
  border-color: #3464e0;
  z-index: 1;
}
.sdc-selector .selector-container--normal {
  padding-top: 3px;
  border-right: none;
  padding-left: 5px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.sdc-selector .selector-container--medium {
  padding-top: 1px;
  height: 36px;
}
.sdc-selector .selector-container--small {
  padding-top: 1px;
  height: 32px;
}
.sdc-selector .selector-container--textarea {
  padding: 5px 10px;
  overflow: auto;
}
.sdc-selector .selector-container:hover:not(.selector-container--focus) {
  border-right: 1px solid #acacac !important;
  border-color: #acacac;
  z-index: 1;
}
.sdc-selector .selector-container .el-loading-spinner {
  margin-top: -10px !important;
}
.sdc-selector .selector-container .container-inner {
  position: relative;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  padding-right: 1px;
}
.sdc-selector .selector-container .container-inner .el-input__suffix {
  display: none;
}
.sdc-selector .selector-container .container-inner .el-input__suffix .el-icon-circle-close {
  cursor: pointer;
  font-size: 16px;
  color: #acacac;
}
.sdc-selector .selector-container .container-inner:hover {
  overflow: visible;
}
.sdc-selector .selector-container .container-inner:hover .el-input__suffix {
  display: block;
}
.sdc-selector .selector-container .container-inner .tags {
  position: absolute;
  height: 45px;
  max-width: 80%;
  white-space: nowrap;
  padding: 0 3px;
  overflow-x: auto;
  overflow-y: hidden;
  line-height: 28px;
}
.sdc-selector .selector-container .container-inner .tags--medium {
  height: 41px;
  line-height: 24px;
}
.sdc-selector .selector-container .container-inner .tags--small {
  height: 37px;
  line-height: 20px;
}
.sdc-selector .selector-container .container-inner .el-tag {
  font-size: 14px;
}
.sdc-selector .selector-container .container-inner .tag {
  margin: 2px 6px 2px 0;
  cursor: pointer;
}
.sdc-selector .selector-container .container-inner .tag.el-tag.el-tag--info {
  background-color: #f2f2f2;
  border-color: #eee;
  color: #666;
}
.sdc-selector .selector-container .container-inner .el-input__inner {
  border: none;
  font-size: 14px;
  padding: 0 0 0 5px;
}
.sdc-selector .selector-container .container-inner .el-input__inner .el-input__icon {
  transition: all 0.3s;
}
.sdc-selector .selector-container .container-inner .el-input__inner:focus + .el-input__suffix {
  display: block;
}
.sdc-selector .selector-container .container-inner .el-autocomplete {
  flex-grow: 1;
  line-height: 24px;
}
.sdc-selector .suffix-open {
  display: inline-block;
  margin-left: -1px;
  line-height: 40px;
  height: 40px;
}
.sdc-selector .suffix-open--medium {
  height: 36px;
  line-height: 36px;
}
.sdc-selector .suffix-open--small {
  height: 32px;
  line-height: 32px;
}
.sdc-selector .suffix-open .el-button {
  padding: 11px 10px;
  height: 40px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.sdc-selector .suffix-open .el-button--medium {
  height: 36px;
  padding: 9px 9px;
}
.sdc-selector .suffix-open .el-button--small {
  height: 32px;
  padding: 7px 8px;
}
.sdc-selector .suffix-num {
  font-size: 14px;
  color: #999;
  margin-left: 9px;
  white-space: nowrap;
}
.el-autocomplete-suggestion li.highlighted .item-name,
.el-autocomplete-suggestion li:hover .item-name,
.el-autocomplete-suggestion li.highlighted .item-count,
.el-autocomplete-suggestion li:hover .item-count {
  color: #3464e0;
}
.el-autocomplete-suggestion .dropdown--empty {
  text-align: center;
  color: #999;
}
.selector-modal .sdc-modal .modal-dialog .modal-body,
.selector-modal-append-to-body .sdc-modal .modal-dialog .modal-body {
  padding-left: 12px;
}
.selector-modal .el-tree-node__content,
.selector-modal-append-to-body .el-tree-node__content {
  height: 42px;
}
.selector-modal .side,
.selector-modal-append-to-body .side {
  box-sizing: border-box;
  width: 50%;
  margin: 10px 0;
}
.selector-modal .left-side,
.selector-modal-append-to-body .left-side {
  box-sizing: border-box;
  width: 50%;
  margin: 10px 0;
  float: left;
  border-right: 1px solid #eee;
  padding-right: 4px;
  overflow: auto;
}
.selector-modal .right-side,
.selector-modal-append-to-body .right-side {
  box-sizing: border-box;
  width: 50%;
  margin: 10px 0;
  float: right;
  padding-left: 20px;
}
.selector-modal .tree-list,
.selector-modal-append-to-body .tree-list {
  height: 450px;
}
.selector-modal .tree-list > .el-tree,
.selector-modal-append-to-body .tree-list > .el-tree {
  display: inline-block;
  min-width: 100%;
}
.selector-modal .tree-list .tree-node,
.selector-modal-append-to-body .tree-list .tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
}
.selector-modal .tree-list .tree-node .el-checkbox,
.selector-modal-append-to-body .tree-list .tree-node .el-checkbox {
  margin-right: 10px;
}
.selector-modal .tree-list .tree-node .el-checkbox .el-checkbox__inner,
.selector-modal-append-to-body .tree-list .tree-node .el-checkbox .el-checkbox__inner {
  width: 16px;
  height: 16px;
  border-color: #ccc;
}
.selector-modal .tree-list .tree-node .el-checkbox .el-checkbox__inner:after,
.selector-modal-append-to-body .tree-list .tree-node .el-checkbox .el-checkbox__inner:after {
  top: 2px;
  left: 5px;
}
.selector-modal .tree-list .tree-node .tree-node-avatar,
.selector-modal-append-to-body .tree-list .tree-node .tree-node-avatar {
  width: 25px;
  border-radius: 50%;
  margin-right: 8px;
}
.selector-modal .tree-list .tree-node .tree-node-text,
.selector-modal-append-to-body .tree-list .tree-node .tree-node-text {
  vertical-align: middle;
  padding-right: 5px;
}
.selector-modal .selected-info,
.selector-modal-append-to-body .selected-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #acacac;
  height: 40px;
  font-size: 14px;
  line-height: 40px;
}
.selector-modal .selected-info .el-icon-delete,
.selector-modal-append-to-body .selected-info .el-icon-delete {
  margin-right: 10px;
  color: #acacac;
  font-size: 16px;
  cursor: pointer;
}
.selector-modal .selected-list,
.selector-modal-append-to-body .selected-list {
  height: 360px;
  overflow: auto;
}
.selector-modal .selected-list .list-item,
.selector-modal-append-to-body .selected-list .list-item {
  color: #666;
  min-height: 42px;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 5px 5px;
  justify-content: space-between;
}
.selector-modal .selected-list .list-item:hover,
.selector-modal-append-to-body .selected-list .list-item:hover {
  background-color: #f5f7f9;
}
.selector-modal .selected-list .list-item .list-item-avatar,
.selector-modal-append-to-body .selected-list .list-item .list-item-avatar {
  width: 25px;
  border-radius: 50%;
  margin-right: 8px;
}
.selector-modal .selected-list .list-item .list-item-name,
.selector-modal-append-to-body .selected-list .list-item .list-item-name {
  line-height: 20px;
  margin-right: 20px;
  vertical-align: middle;
}
.selector-modal .selected-list .list-item .list-item-icon,
.selector-modal-append-to-body .selected-list .list-item .list-item-icon {
  color: #999;
  cursor: pointer;
  margin-right: 3px;
}
.selector-modal .selected-list .list-item .list-item-icon i.el-icon-error,
.selector-modal-append-to-body .selected-list .list-item .list-item-icon i.el-icon-error {
  font-size: 18px;
}
.selector-modal .modal-buttons,
.selector-modal-append-to-body .modal-buttons {
  padding: 5px 0;
  margin-top: 15px;
  text-align: right;
}
.selector-modal .modal-buttons .el-button,
.selector-modal-append-to-body .modal-buttons .el-button {
  width: 80px;
}
.selector-modal .modal-buttons .el-button:first-child,
.selector-modal-append-to-body .modal-buttons .el-button:first-child {
  margin-right: 10px;
}
.selector-modal-append-to-body.sdc-modal .modal-dialog .modal-body {
  padding-left: 12px;
}
.sdc-svg-icon {
  color: #acacac;
}
.sdc-svg-icon:hover,
.sdc-svg-icon.hover {
  color: #3464e0;
}
.sdc-svg-icon:hover use.stroke,
.sdc-svg-icon.hover use.stroke {
  stroke: #3464e0;
}
.sdc-svg-icon use.stroke {
  stroke: #acacac;
}
.sdc-svg-icon svg {
  overflow: hidden;
  width: 32px;
  height: 32px;
  vertical-align: -0.15em;
  fill: currentColor;
}
.sdc-svg-icon svg.svg-sm {
  width: 16px;
  height: 16px;
}
.sdc-svg-icon svg.svg-md {
  width: 24px;
  height: 24px;
}
.sdc-svg-icon svg.svg-lg {
  width: 32px;
  height: 32px;
}
.sdc-svg-icon svg.svg-xl {
  width: 48px;
  height: 48px;
}
.sdc-modal {
  position: fixed;
  top: 15%;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  transition: all 0.5s;
}
.sdc-modal.modal-scroll {
  top: 20%;
}
.sdc-modal.modal-scroll .modal-dialog,
.sdc-modal.modal-scroll.slide-enter-active {
  top: 20%;
}
.sdc-modal.slide-enter-active {
  top: 15%;
}
.sdc-modal.slide-enter,
.sdc-modal.slide-leave-active {
  top: -100%;
}
.sdc-modal .mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 9999;
  filter: alpha(opacity=75);
  -moz-opacity: 0.75;
  opacity: 0.75;
}
.sdc-modal .modal-dialog {
  position: absolute;
  top: 15%;
  left: 50%;
  height: auto;
  border-radius: 6px;
  overflow-y: hidden;
  z-index: 10000;
  transform: translate(-50%, -40%);
  background-color: #fff;
}
.sdc-modal .modal-dialog .modal-header,
.sdc-modal .modal-dialog .modal-footer {
  padding: 0 20px;
  background-color: #fff;
}
.sdc-modal .modal-dialog .modal-header {
  display: block;
  position: relative;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
  border-bottom: 1px solid #f5f7f9;
}
.sdc-modal .modal-dialog .modal-header .title {
  color: #333;
  font-size: 16px;
  font-weight: bold;
}
.sdc-modal .modal-dialog .modal-header .close {
  position: absolute;
  display: inline-block;
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
  cursor: pointer;
}
.sdc-modal .modal-dialog .modal-body {
  overflow-y: auto;
  padding: 10px 20px;
  color: #666;
}
.sdc-modal .modal-dialog .modal-footer {
  height: 60px;
  line-height: 50px;
  text-align: right;
  margin-top: -10px;
}
.sdc-modal .modal-dialog .modal-footer .btn-group button {
  min-width: 80px;
  padding: 10px 15px;
  font-size: 12px;
  border-radius: 3px;
  margin-right: 5px;
}
.sdc-modal .modal-dialog .modal-footer .btn-group button:last-child {
  margin-right: 0;
}
.sdc-alert .modal-header {
  border-bottom: none !important;
}
.sdc-alert .content {
  font-size: 14px;
  text-align: center;
  line-height: 1.5em;
  margin-bottom: 1.5em;
}
.sdc-modal.sdc-prompt .modal-dialog .modal-header {
  border-bottom: none;
}
.sdc-modal.sdc-prompt .modal-dialog .modal-body {
  padding-top: 0;
}
.sdc-modal.sdc-prompt .content {
  font-size: 14px;
  line-height: 1.5em;
  margin-bottom: 0.5em;
}
.sdc-router-view {
  height: 100%;
}
.sdc-router-view .page-container {
  width: 100%;
  height: 100%;
  overflow: auto;
}
.sdc-query-panel.el-card {
  border: none;
  border-left: 1px solid #f5f7f9;
  margin-bottom: 10px;
}
.sdc-query-panel.el-card .el-card__body {
  padding: 15px 15px 0;
}
.sdc-query-panel.el-card .el-row.action {
  padding-bottom: 15px;
  text-align: right;
}
.sdc-query-panel.el-card .el-row.action.expand {
  padding-top: 15px;
  border-top: 1px dashed #dcdcdc;
}
.sdc-query-panel.el-card .el-form.el-form--inline {
  display: flex;
  justify-content: space-between;
}
.sdc-query-panel.el-card .el-form.el-form--inline .content {
  display: inline-block;
}
.sdc-query-panel.el-card .el-form.el-form--inline .content .el-input__inner {
  width: 330px;
}
.sdc-query-panel.el-card .el-form.el-form--inline .content .fl-date-range {
  width: 330px;
}
.sdc-query-panel.el-card .el-form.el-form--inline .content .fl-date-range .el-input__inner {
  width: 150px;
}
.sdc-query-panel.el-card .el-form.el-form--block .el-select,
.sdc-query-panel.el-card .el-form.el-form--block .el-date-editor.el-input__inner {
  width: 100%;
}
.sdc-query-panel.el-card .sdc-selector {
  margin-top: 2px;
}
.sdc-query-panel.el-card .sdc-selector .selector-container--normal .el-autocomplete .el-input__inner,
.sdc-query-panel.el-card .sdc-selector .selector-container--medium .el-autocomplete .el-input__inner {
  height: 32px;
  line-height: 32px;
}
.sdc-query-panel.el-card .sdc-selector .selector-container--small .el-autocomplete .el-input__inner {
  height: 28px;
  line-height: 28px;
}
.sdc-query-panel.el-card .el-form-item .el-form-item__label,
.sdc-query-panel.el-card .el-form-item .el-input__inner,
.sdc-query-panel.el-card .el-form-item .el-range-input {
  font-size: 13px;
}
.sdc-query-panel.el-card .el-form-item .el-input__inner {
  height: 34px;
  line-height: 34px;
  padding-left: 10px;
}
.sdc-query-panel.el-card .el-form-item .el-input__icon.el-icon-date,
.sdc-query-panel.el-card .el-form-item .el-date-editor .el-range-separator {
  font-size: 13px;
  line-height: 27px;
}
.sdc-query-panel.el-card .el-form-item .el-date-editor .el-range-separator {
  width: auto;
}
.sdc-query-panel.el-card .action button {
  min-width: 80px;
  padding: 10px 15px;
  font-size: 12px;
  border-radius: 3px;
}
.sdc-query-panel.el-card .expand-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 34px;
  height: 34px;
  text-align: left;
  border-radius: 50%;
  cursor: pointer;
  background: #dcdfe6;
}
.sdc-query-panel.el-card .expand-icon:hover {
  background: #1890ff;
  color: #fff;
}
.sdc-staff-selector-popper {
  width: 260px!important;
}
.sdc-staff-selector-popper .selector-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: normal;
  margin: 10px 0;
}
.sdc-staff-selector-popper .selector-item .item-name {
  text-overflow: ellipsis;
  overflow: hidden;
  margin-left: 8px;
  white-space: nowrap;
  color: #666;
  line-height: 25px;
}
.sdc-staff-selector-popper .selector-item .item-avatar {
  width: 25px;
  border-radius: 50%;
  height: 25px;
  flex-shrink: 0;
}
.sdc-staff-selector-popper .selector-item .item-former-name {
  flex-shrink: 0;
  height: 14px;
  width: 14px;
  margin-left: 8px;
  background: url("../img/former-name.svg");
}
.sdc-unit-selector-popper {
  width: 370px!important;
}
.sdc-unit-selector-popper .selector-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  line-height: normal;
  margin: 10px 0;
  align-items: center;
}
.sdc-unit-selector-popper .selector-item .item-name {
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
  color: #666;
}
.sdc-unit-selector-popper .selector-item .item-count {
  margin-left: 40px;
  color: #999;
}
.sdc-post-selector-popper {
  width: 300px!important;
}
.sdc-post-selector-popper .selector-item {
  line-height: normal;
  margin: 10px 0;
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
  color: #666;
}
.sdc-cascader {
  display: flex;
  align-items: center;
}
.sdc-cascader .cascader-container {
  width: 0;
  flex: 1;
  padding-top: 1px;
  background-color: #fff;
  height: 40px;
  position: relative;
  border: 1px solid #dcdcdc;
  display: flex;
  border-radius: 4px;
  overflow: hidden;
}
.sdc-cascader .cascader-container.is-disabled {
  background-color: #f2f2f2;
}
.sdc-cascader .cascader-container--focus {
  border-color: #3464e0;
}
.sdc-cascader .cascader-container--focus .cascader-container-right-icon.el-icon-arrow-down {
  transform: rotate(180deg);
  transition: all 0.3s;
}
.sdc-cascader .cascader-container--small {
  height: 36px;
}
.sdc-cascader .cascader-container:hover {
  overflow: visible;
}
.sdc-cascader .cascader-container:hover .cascader-container-right-icon.el-icon-circle-close {
  display: block;
}
.sdc-cascader .cascader-container:hover .cascader-container-right-icon.el-icon-arrow-down.is-show-close {
  display: none;
}
.sdc-cascader .cascader-container.cascader-placeholder::after {
  position: absolute;
  content: attr(placeholder);
  color: #999;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  left: 15px;
  top: calc(50% - 10px);
}
.sdc-cascader .cascader-container .cascader-container-right-icon {
  position: absolute;
  color: #999;
  right: 10px;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  top: calc(50% - 10px);
}
.sdc-cascader .cascader-container .cascader-container-right-icon.el-icon-circle-close {
  display: none;
  cursor: pointer;
}
.sdc-cascader .cascader-container .el-icon-circle-close {
  font-size: 14px;
}
.sdc-cascader .cascader-container .tags {
  margin-top: 2px;
  height: 45px;
  max-width: calc(100% - 100px);
  white-space: nowrap;
  padding: 0 3px;
  overflow-x: auto;
  overflow-y: hidden;
  line-height: 28px;
}
.sdc-cascader .cascader-container .tags--small {
  height: 41px;
  line-height: 24px;
}
.sdc-cascader .cascader-container .tags--empty {
  padding: 0;
}
.sdc-cascader .cascader-container .tag {
  margin: 2px 6px 2px 0;
  cursor: pointer;
}
.sdc-cascader .cascader-container .tag.el-tag {
  transition: none !important;
}
.sdc-cascader .cascader-container .tag:last-child {
  margin-right: 0;
}
.sdc-cascader .cascader-container .el-tag--info {
  background-color: #f2f2f2;
  border-color: #eee;
  color: #666;
}
.sdc-cascader .cascader-container .el-cascader {
  flex: 1;
}
.sdc-cascader .cascader-container .el-cascader .el-tag {
  display: none;
}
.sdc-cascader .cascader-container .el-cascader .el-input {
  margin-top: 2px;
}
.sdc-cascader .cascader-container .el-cascader .el-input--mini .el-input__inner {
  height: 30px !important;
}
.sdc-cascader .cascader-container .el-cascader .el-input__inner {
  border: none;
}
.sdc-cascader .cascader-container .el-cascader .el-cascader__search-input {
  margin: 2px 0 2px 5px;
  padding-left: 10px;
}
.sdc-cascader .cascader-container--not-filterable .tags {
  max-width: calc(100% - 30px);
}
.sdc-cascader .cascader-container--not-filterable:not(.sdc-cascader .cascader-container.is-disabled) {
  cursor: pointer;
}
.sdc-cascader .suffix-num {
  font-size: 14px;
  color: #999;
  margin-left: 9px;
  white-space: nowrap;
  flex-shrink: 0;
}
.el-cascader__suggestion-list {
  min-width: 300px !important;
}
.cascader-filterable-popper .el-checkbox__inner {
  overflow: hidden;
}
.cascader-filterable-popper .el-cascader-menu__empty-text i {
  display: none;
}
.cascader-filterable-popper--loading .el-cascader-menu__empty-text span {
  display: none;
}
.cascader-filterable-popper--loading .el-cascader-menu__empty-text i {
  font-size: 16px;
  display: block;
}
.sdc-dict-selector {
  display: inline-block;
}
.sdc-dict-selector .el-tag.el-tag--info {
  background-color: #f2f2f2;
  border-color: #eee;
  color: #666;
}
.sdc-dict-selector .el-tag__close.el-icon-close {
  background-color: #f2f2f2;
}
.sdc-exception-page {
  width: 100%;
  margin-top: 20%;
  text-align: center;
}
.sdc-exception-page .wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.sdc-exception-page .wrapper .content {
  padding: 0 20px;
  line-height: 3.6;
  font-size: 14px;
  text-align: left;
}
.sdc-exception-page .wrapper .content .title {
  color: #333;
  font-weight: 700;
  font-size: 18px;
}
.sdc-exception-page .wrapper .content .links a {
  color: #3464e0;
}
.sdc-exception-page .wrapper .content .links .or {
  margin: 0 10px;
}
.sdc-exception-page .wrapper .content .info {
  color: #666;
}
.sdc-avatar {
  display: flex;
  margin-left: 5px;
  height: 100%;
  align-items: center;
  cursor: pointer;
}
.sdc-avatar .sdc-avatar-inner {
  display: flex;
  align-items: center;
  color: #fff;
}
.sdc-avatar .sdc-avatar-inner .el-icon-arrow-down {
  font-size: 12px;
}
.sdc-avatar .avatar-item {
  position: relative;
  display: flex;
  align-items: center;
}
.sdc-avatar .avatar-item .name {
  margin-left: 10px;
}
.sdc-avatar-dropdown {
  min-width: 100px;
}
.sdc-avatar-dropdown.hidden {
  display: none !important;
}
.sdc-wxheader .logo-text {
  padding-left: 10px;
  font-size: 16px;
  font-weight: bold;
}
.sdc-wxheader .ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.sdc-wxheader .header-icons {
  display: flex;
  margin-right: 20px;
}
.sdc-wxheader .header-icons a {
  display: flex !important;
  align-items: center;
  margin-right: 30px;
}
.sdc-wxheader .header-icons a:last-child {
  margin-right: 0px;
}
.sdc-wxheader .header-icons a i {
  font-size: 20px;
  margin-right: 5px;
}
.sdc-wxheader .header-icons a i.icon-appstore {
  display: inline-block;
  line-height: 1;
  width: 20px;
  height: 20px;
  background-image: url("../img/icon-appstore.svg");
}
