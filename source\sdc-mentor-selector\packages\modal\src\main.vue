<template>
  <transition name="slide">
    <div class="sdc-modal sdc-modal-customer" :class="modalClass" v-show="showModal">
      <div class="mask"></div>
      <div class="modal-dialog" :style="{'width':modalDialogWidth,'max-height':modalHeight}" v-loading="checkRiskLoading">
        <div class="modal-header" v-if="showHeader">
          <slot name="header">
            <span class="title">{{title}}</span>
          </slot>
          <sdc-svg-icon class="close" name="close" width="16px" height="16px" @click.native="hide('close')" v-if="showClose"/>
        </div>
        <div class="modal-body" :style="{'max-height':modalBodyHeight}">
          <slot name="body"></slot>
        </div>
        <div class="modal-footer" v-if="showFooter">
          <slot name="footer">
            <div class="btn-group">
              <slot name="buttons">
                <el-button size="small" @click="hide('cancel')">{{cancelText || $st('sdc.modal.cancel')}}</el-button>
                <el-button type="primary" size="small" @click="$emit('ok')" v-if="showOk">{{okText || $st('sdc.modal.ok')}}</el-button>
              </slot>
            </div>
          </slot>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
  // import 'packages/theme-grace/icon/close.svg'
  import { locale } from 'mixins'
  import SdcSvgIcon from 'packages/svg-icon'
  import { addResizeListener, removeResizeListener } from '../../../src/utils/resize'

  export default {
    name: 'sdc-modal',
    mixins: [locale],
    props: {
      title: String,
      // 如应用该组件的样式采用scoped，请在子元素上使用/deep/完成穿透
      customClass: String,
      showClose: {
        type: Boolean,
        default: true
      },
      showOk: {
        type: Boolean,
        default: true
      },
      showHeader: {
        type: Boolean,
        default: true
      },
      showFooter: {
        type: Boolean,
        default: true
      },
      size: {
        type: String,
        default: 'md' // sm, md, lg, xl
      },
      adaptive: {
        type: Boolean,
        default: false
      },
      width: String, // 对话框的指定宽度(或百分比)
      okText: String,
      cancelText: String,
      appendToBody: {
        type: Boolean,
        default: false
      },
      checkRiskLoading: { // 搜索导师时加载loading
        type: Boolean,
        default: false
      }
    },
    data: () => ({
      showModal: false,
      modalSizes: [
        { size: 'sm', value: '300px' },
        { size: 'md', value: '600px', default: true },
        { size: 'lg', value: '900px' },
        { size: 'xl', value: '1200px' }
      ],
      clientHeight: document.documentElement.clientHeight
    }),
    computed: {
      modalDialogWidth() {
        if (this.width) {
          const width = this.width + ''
          const units = ['%', 'px', 'vw', 'em', 'rem']
          return units.some(item => width.endsWith(item)) ? width : parseFloat(width) + 'px'
        }
        const item = this.modalSizes.find(item => item.size === this.size) || this.modalSizes.find(item => item.default)
        return item.value
      },
      modalBodyHeight() {
        return `${parseInt(this.modalHeight) - 100}px`
      },
      modalHeight() {
        return `${this.clientHeight * 0.9}px`
      },
      modalClass() {
        return `${this.adaptive ? 'modal-scroll' : ''} ${this.customClass}`
      }
    },
    mounted() {
      addResizeListener(this.$el, this.handleResize)
    },
    destroyed() {
      removeResizeListener(this.$el, this.handleResize)
      if (this.appendToBody && this.$el && this.$el.parentNode) {
        this.$el.parentNode.removeChild(this.$el)
      }
    },
    methods: {
      show() {
        this.$emit('show')
        this.showModal = true
        if (this.appendToBody) {
          document.body.appendChild(this.$el)
        }
      },
      hide(evt = '') {
        this.showModal = false
        this.$emit('reset')
        evt && this.$emit(evt)
      },
      handleResize() {
        this.clientHeight = document.documentElement.clientHeight
      }
    },
    components: {
      SdcSvgIcon
    }
  }
</script>

<style lang="less" scoped>
.sdc-modal-customer {
  .modal-dialog {
    max-width: 750px;
    max-height: 540px !important;
    overflow: hidden;
    .modal-header {
      display: flex;
      align-items: center;
      height: 65px !important;
    }
    .modal-body {
      overflow: hidden;
      padding: 0 !important;
    }
  }
} 
</style>
