<template>
  <svg class="sdc-svg-icon" :class="[customClass, size ? 'svg-' + size : '']" :style="{width, height}" @click="$emit('click')">
    <use :xlink:href="`#${name}`"></use>
  </svg>
</template>

<script>
export default {
  name: 'sdc-svg-icon',
  props: {
    name: {
      type: String,
      required: true
    },
    size: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: ''
    },
    customClass: {
      type: String,
      default: ''
    }
  }
}
</script>
