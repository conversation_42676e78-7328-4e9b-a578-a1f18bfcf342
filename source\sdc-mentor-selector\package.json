{"name": "@tencent/sdc-mentor-test", "version": "0.0.81", "description": "The package is provided web components and utilities based on vue and element-ui.", "main": "lib/index.js", "files": ["lib", "src", "packages"], "unpkg": "lib/index.js", "author": "Tencent SDC Team", "license": "MIT", "repository": {"type": "git", "url": "http://git.code.oa.com/SDCFront/sdc-webui.git"}, "homepage": "http://sdcwebui.pages.oa.com", "keywords": ["npm", "frontend", "framework", "@tencent", "@tencent/sdc-core", "@tencent/sdc-vue"], "scripts": {"bootstrap": "tnpm i", "clean": "rimraf lib & rimraf packages/*/lib", "lint": "eslint src/**/* packages/**/* build/**/* --quiet", "lint:fix": "tnpm run lint --fix", "new": "node build/bin/build-new.js", "i18n": "node build/bin/build-i18n.js", "check": "tnpm run clean & tnpm run lint", "webpack": "webpack --config build/conf/webpack.umd.js & webpack --config build/conf/webpack.module.js & webpack --config build/conf/webpack.component.js", "build:file": "node build/bin/build-entry.js & tnpm run i18n", "build:locale": "node build/bin/build-locale.js", "build:theme": "node build/bin/build-css.js && gulp build --gulpfile gulpfile.js && cp-cli packages/theme-grace/img lib/img && cp-cli packages/theme-grace/icon lib/icon", "build:utils": "cross-env BABEL_ENV=utils babel src --out-dir lib --ignore src/index.js", "dev": "tnpm run check & tnpm run build:file & cross-env NODE_ENV=development tnpm run webpack & tnpm run build:utils & tnpm run build:locale & tnpm run build:theme", "build": "tnpm run check & tnpm run build:file & cross-env NODE_ENV=production tnpm run webpack & tnpm run build:utils & tnpm run build:locale & tnpm run build:theme", "demo": "tnpm run build:file & cross-env NODE_ENV=development webpack-dev-server --config build/conf/webpack.demo.js & node build/bin/build-demo.js", "demo:play": "tnpm run build:file & cross-env NODE_ENV=development PLAY_ENV=true webpack-dev-server --config build/conf/webpack.demo.js", "demo:build": "rimraf docs & tnpm run build:file & cross-env NODE_ENV=production webpack --config build/conf/webpack.demo.js", "pub": "npm publish && npm version patch"}, "dependencies": {"@babel/polyfill": "^7.12.1", "async-validator": "~1.8.1", "babel-helper-vue-jsx-merge-props": "^2.0.0", "better-scroll": "^1.13.2", "deepmerge": "^1.2.0", "normalize-wheel": "^1.0.1", "resize-observer-polyfill": "^1.5.1", "throttle-debounce": "^1.0.1", "vue-fragment": "1.5.1", "whatwg-fetch": "^3.5.0"}, "devDependencies": {"babel-plugin-transform-runtime": "6.23.0", "@tencent/sdc-core": "^1.0.7", "@tencent/sdc-theme": "^1.0.2", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/component-compiler-utils": "^2.6.0", "@vue/eslint-config-standard": "^5.1.2", "algoliasearch": "^3.24.5", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-eslint": "^10.1.0", "babel-loader": "^7.1.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-module-resolver": "^2.2.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.24.1", "babel-regenerator-runtime": "^6.5.0", "chai": "^4.2.0", "chokidar": "^1.7.0", "concurrently": "^5.2.0", "copy-webpack-plugin": "^5.0.0", "coveralls": "^3.0.3", "cp-cli": "^1.1.2", "cross-env": "^7.0.2", "css-loader": "^2.1.0", "element-ui": "^2.13.0", "es6-promise": "^4.0.5", "eslint": "4.18.2", "eslint-loader": "^2.0.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^5.0.0", "file-loader": "^1.1.11", "file-save": "^0.2.0", "gulp": "^4.0.0", "gulp-autoprefixer": "^6.0.0", "gulp-cssmin": "^0.2.0", "gulp-less": "^4.0.1", "highlight.js": "^9.3.0", "html-webpack-plugin": "^3.2.0", "json-loader": "^0.5.7", "json-templater": "^1.0.4", "less": "^3.8.1", "less-loader": "^4.1.0", "markdown-it": "^8.4.1", "markdown-it-anchor": "^5.0.2", "markdown-it-chain": "^1.3.0", "markdown-it-container": "^2.0.0", "mini-css-extract-plugin": "^0.4.1", "nopt": "^4.0.1", "optimize-css-assets-webpack-plugin": "^5.0.1", "postcss": "^7.0.14", "progress-bar-webpack-plugin": "^1.11.0", "rimraf": "^2.5.4", "select-version-cli": "^0.0.2", "sinon": "^7.2.7", "sinon-chai": "^3.3.0", "style-loader": "^0.23.1", "svg-sprite-loader": "^4.2.1", "transliteration": "^1.1.11", "uglifyjs-webpack-plugin": "^2.1.1", "uppercamelcase": "^1.1.0", "url-loader": "^1.1.2", "vue": "2.5.21", "vue-loader": "^15.7.0", "vue-router": "^3.0.1", "vue-template-compiler": "2.5.21", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^4.14.0", "webpack-cli": "^3.0.8", "webpack-dev-server": "^3.1.11", "webpack-node-externals": "^1.7.2"}, "peerDependencies": {"@tencent/sdc-core": "^1.0.7", "@tencent/sdc-theme": "^1.0.2", "element-ui": "^2.13.0", "vue": "^2.5.17"}}