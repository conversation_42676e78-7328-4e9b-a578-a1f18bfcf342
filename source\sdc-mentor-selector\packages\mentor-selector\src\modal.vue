<template>
  <div class="selector-modal selector-modal-customer">
    <sdc-modal ref="modal" :checkRiskLoading="checkRiskLoading || wrongLoaading" :title="modalProps.title" :width="modalWidth || '750px'" adaptive :showFooter="false"
      :customClass="modalClass" :appendToBody="modalAppendToBody" :class="{'selector-modal-append-to-body': modalAppendToBody}" @close="closeDialog" @show="showDialog">
      <div slot="header">
        <div class="header-main">
          <div class="header-text">导师选择</div>
          <div class="header-warm">仅支持选择权限范围内组织下的正式员工及顾问</div>
        </div>
      </div>
      <div slot="body">
        <template v-if="requireWrongNum > 0 && requireWrongNum < 3">
          <div class="no-power-content">
            <img class="no-power-img" :src="require('packages/theme-grace/img/no-power-img.png')" alt="" srcset="">
            <div class="no-power-text">无法获取当前操作人权限范围，<el-button class="refresh-btn" :loading="false" type="text" @click="operatorRightCheck(true)">请点击刷新重试</el-button></div>
          </div>
        </template>
        <template  v-else>
          <div class="left-side">
            <div class="search-main">
              <el-autocomplete
                class="inline-input-left-side"
                v-model="queryText"
                ref="input"
                :trigger-on-focus="false"
                :fetch-suggestions="getSuggetions"
                :debounce="500"
                :value-key="valueKey"
                size="small"
                placeholder="请输入中/英文名"
                @focus="focus=true"
                @blur="handleblur"
                @select="handleSelect"
                suffix-icon="el-icon-search"
                :popper-class="`sdc-${mode}-selector-popper`"
                >
                <template slot-scope="{ item }">
                  <p class="dropdown--empty" v-if="item.empty">{{item.message}}</p>
                  <slot v-else name="item" :data="{option:item,keyword:queryText}"/>
                </template>
              </el-autocomplete>
              <div class="empty-trip-customer" v-if="focus && queryText && searchEmptyText">{{ searchEmptyText }}</div>
            </div>
            <div class="tree-list">
              <el-tree ref="tree" :check-strictly="true" :props="treeProps" :data="treeData" lazy node-key="id" :load="loadNode" v-if="opened" v-loading="loading" @node-click="nodeClick">
                <span class="tree-node" slot-scope="{ node, data }">
                  <span>
                    <img :src="data[map.avatar] || avatarUrl" v-on:error="notFound" v-if="data.type===map.type.staff" class="tree-node-avatar">
                    <span class="tree-node-text">{{ node.label }}</span>
                  </span>
                  <el-checkbox v-model="node.checked" v-if="multiple || data.type === map.type.staff" :indeterminate="node.indeterminate" @change="handleCheckChange(node)" @click.native.stop/>
                </span>
              </el-tree>
            </div>
          </div>
          <div class="right-side">
            <!-- <div class="selected-info">
              <span>{{selectedItemsText}}</span>
              <i class="el-icon-delete" @click="handleClear" v-show="selectedData.length"></i>
            </div> -->
            <div class="selected-risk-main">
              <div class="selected-list" :key="reloadKey">
                <div class="list-item" v-for="item in getSelectedItems(selectedData)" :key="item[map.staffID]">
                  <el-tooltip placement="top-end" :content="item._text" :disabled="item._text.length <= item._modal.minLength" popper-class="sdc-selector-modal-popper">
                    <span>
                      <img :src="item[map.avatar] || avatarUrl" v-on:error="notFound" class="list-item-avatar">
                      <span class="list-item-name">{{textEllipsis(item._text, item._modal) }}</span>
                    </span>
                  </el-tooltip>
                  <span @click="handleDelete(item)" class="list-item-icon"><i class="el-icon-error"></i></span>
                </div>
              </div>
              <!-- 风险结果 -->
              <div class="risk-outcomm-main" v-if="getSelectedItems(selectedData) && getSelectedItems(selectedData).length">
                <template v-if="!riskResult.getRiskFail">
                  <div class="risk-outcomm-title" :class="riskClass" v-if="showRiskTitle">
                    <img class="risk-icon" :src="riskMap[riskResult.check_result].img" alt="">
                    <div class="risk-text">{{ riskMap[riskResult.check_result].text  }}</div>
                  </div>
                  <div class="risk-desc" ref="riskDesc" v-if="[3, '3'].includes(riskResult.check_result) && !talentAndSelf">
                    <div class="risk-desc-item" v-if="noThreshold">
                      <div class="risk-desc-title">{{ noList.indexOf('threshold') }}. 不满足以下门槛资格：<span class="risk-desc-title-r">（职级和绩效数据存在T+1延迟）</span></div>
                      <div class="risk-desc-subitem">
                        <div class="risk-desc-subitem-text" v-for="(item, index) in riskResult.threshold_risk_detail" :key="index">
                          <!-- <el-tooltip class="item" effect="dark" :content="item.requirements_content" placement="top-end" :disabled="calcStrNum(item.requirements_content) < minHidNum">
                            <div class="risk-tooltip-inner">
                              <img class="risk-item-icon" :src="require('packages/theme-grace/img/red-close.png')" alt=""><span class="risk-item-text">{{ item.requirements_content }}</span>
                            </div>
                          </el-tooltip> -->
                          <el-popover
                            popper-class="sdc-mentor-selector-popover-customer-item"
                            placement="top-end"
                            title=""
                            trigger="hover"
                            :transition="null"
                            :close-delay="0"
                            :disabled="calcStrNum(item.requirements_content) < minHidNum"
                            :content="item.requirements_content">
                            <div slot="reference" class="risk-tooltip-inner">
                              <img class="risk-item-icon" :src="require('packages/theme-grace/img/red-close.png')" alt=""><span class="risk-item-text">{{ item.requirements_content }}</span>
                            </div>
                          </el-popover>
                        </div>
                      </div>
                    </div>
                    <div class="risk-desc-item" v-if="noCert">
                      <div class="risk-desc-title">{{ noList.indexOf('cert') }}. 不满足以下认证要求：</div>
                      <div class="risk-desc-subitem">
                        <div class="risk-desc-subitem-text" v-for="(item, index) in riskResult.cert_risk_detail" :key="index">
                          <!-- <el-tooltip class="item" effect="dark" :content="item.requirements_content" placement="top-end" :disabled="calcStrNum(item.requirements_content) < minHidNum">
                            <div class="risk-tooltip-inner">
                              <img class="risk-item-icon" :src="require('packages/theme-grace/img/red-close.png')" alt=""><span @click="toAuthenticationPage(item.course_url)" class="risk-item-text risk-2-link">{{ item.requirements_content }}</span>
                            </div>
                          </el-tooltip> -->
                          <el-popover
                            popper-class="sdc-mentor-selector-popover-customer-item"
                            placement="top-end"
                            title=""
                            trigger="hover"
                            :transition="null"
                            :close-delay="0"
                            :disabled="calcStrNum(item.requirements_content) < minHidNum"
                            :content="item.requirements_content">
                            <div slot="reference" class="risk-tooltip-inner">
                              <img class="risk-item-icon" :src="require('packages/theme-grace/img/red-close.png')" alt=""><span @click="toAuthenticationPage(item.course_url)" class="risk-item-text risk-2-link">{{ item.requirements_content }}</span>
                            </div>
                          </el-popover>
                        </div>
                      </div>
                    </div>
                    <div class="risk-desc-item" v-if="moreStudents">
                      <div class="risk-desc-title"><span class="q-no">{{ noList.indexOf('student') }}. </span><span>导师正在辅导3位及以上处于辅导期的新员工：{{ riskResult.student_names.join('、') }}</span></div>
                    </div>
                    <div class="risk-desc-item" v-if="isDiffDepart">
                      <div class="risk-desc-title">
                        <span class="q-no">{{ noList.indexOf('depart') }}.</span>
                        <span>
                            导师与新员工所属组织不同：<br />
                            导师所属组织：
                            <template v-if="riskResult.tutor_dept">{{ riskResult.tutor_dept }}</template>
                            <span v-else class="empty-text">暂无数据</span>
                            <br />
                            员工所属组织：
                            <template v-if="riskResult.student_dept_name">{{ riskResult.student_dept_name }}</template>
                            <span v-else class="empty-text">暂无数据</span>
                        </span>
                      </div>
                    </div>
                    <div class="risk-desc-item" v-if="isDiffWorkPlace">
                      <div class="risk-desc-title">
                        <span class="q-no">{{ noList.indexOf('workPlace') }}.</span>
                        <span>
                          导师与新员工工作地不同：<br/>
                           导师工作地：
                            <template v-if="riskResult.tutor_work_place"> {{ riskResult.tutor_work_place }}</template> 
                            <span v-else class="empty-text">暂无数据</span>
                            <br/>
                           新员工工作地：
                           <template v-if="workPlaceInfo.placeName">{{ workPlaceInfo.placeName }}</template>
                           <span v-else class="empty-text">暂无数据</span>
                        </span>
                      </div>
                    </div>
                    <div class="risk-reason" v-if="noThreshold">
                      不符合门槛资格时必须填写匹配原因：
                      <el-input class="risk-reason-input" type="textarea" v-model="riskReason" :autosize="{ minRows: 2 }" placeholder="请输入匹配原因，不超过500中文字符" @input="changeRiskReason"></el-input>
                      <div class="risk-char-count">{{handleValidor(riskReason, 500)}}/500</div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="risk-fail">
                    <img class="risk-fail-img" :src="require('packages/theme-grace/img/risk-fail.png')" alt="">
                    <div class="risk-fail-title">匹配风险判断失败，请刷新重试</div>
                    <el-button class="risk-refresh-button" :loading="false" type="text" @click="refresh">点击刷新</el-button>
                  </div>
                </template>
              </div>
            </div>
            <div class="modal-buttons">
              <el-button size="small" @click="cancel">{{$st('sdc.selector.modal.cancel')}}</el-button>
              <el-button size="small" type="primary" :disabled="!canConfirm" @click="handleConfirm">{{$st('sdc.selector.modal.ok')}}</el-button>
            </div>
          </div>
        </template>
      </div>
    </sdc-modal>
  </div>
</template>

<script>
  import { DataUtil, DataType } from 'sdc-core'
  import { textEllipsis, hasOwn, isNotLogin } from 'src/utils/main'
  import { modal, locale, map, encryption } from 'mixins'
  import SetImg from 'directives/set-img'
  import Toast from 'packages/toast'
  import sdcModal from 'packages/modal'
  import http from 'api/https.js'
  
  export default {
    name: 'selector-modal',
    inject: ['getWrongLoaading', 'operatorRightCheck', 'getWrongNum', 'getPlatform', 'getTypeId', 'getOperatorRightMap', 'specialPlatform', 'getBaseUrl', 'setRiskResult', 'getDisabledMentorList', 'valueKey', 'getSelected', 'getDataList', 'filterKey', 'multiple', 'textarea', 'nodeKey', 'map', 'change', 'selectedText', 'treeProps', 'modalProps', 'queryParams', 'getTreeData', 'getChildrenData', 'getCurrentItem', 'getRange', 'modalClass', 'modalWidth', 'defaultExpandedKeys', 'modalAppendToBody'],
    mixins: [modal, locale, map, encryption],
    directives: {
      setImg: SetImg
    },
    props: {
      data: {
        type: Array,
        default: () => []
      },
      selectedNew: {
        type: Array,
        default: () => []
      },
      riskResultProp: { // 检验风险的结果 是否符合认证要求：DISABLED(-1, "禁用"),QUALIFIED(1, "合格"),RISKY(3, "风险");L4Manage(4, "L4以上高管");
        type: Object,
        default: () => {}
      },
      workPlaceInfo: { // 员工工作地信息
        type: Object,
        default: () => {} 
      },
      deptId: { // 员工最小组织id
        type: [String, Number],
        default: ''
      },
      talentAndSelf: { // 是否是人才透视系统 并且是自己修改
        type: Boolean,
        default: false
      },
      failNum: { // 导师校验失败的次数
        type: Number,
        default: 1
      },
      studentStaffId: { // 新员工staffId
        type: [String, Number],
        default: ''
      },
      student_resume_id: { // 新员工简历id
        type: [String, Number],
        default: ''
      },
      // operatorId: { // 新员工简历id
      //   type: [String, Number],
      //   default: ''
      // },
      mode: String // 选择器类别(如: unit, staff, post)
    },
    data() {
      return {
        focus: false,
        minHidNum: 27, // 超过27个字时超出隐藏显示tooltip
        selectedCount: 0,
        selectedData: [],
        treeData: [],
        opened: false,
        loading: false,
        resolveTree: null,
        lastLimitUnitID: '',
        reloadKey: 0,
        queryText: '',
        isEnter: false,
        riskResult: {}, // // 检验风险的结果 是否符合认证要求：DISABLED(-1, "禁用"),QUALIFIED(1, "合格"),RISKY(3, "风险");L4Manage(4, "L4以上高管");
        riskMap: {
          '-1': { text: '该导师处于“禁用”状态，不可匹配，请重新选择其他导师。如有疑问，可咨询此员工所在部门BP伙伴。', img: require('packages/theme-grace/img/icon-red.png') },
          '1': { text: '该员工已符合导师资格要求', img: require('packages/theme-grace/img/icon-green.png') },
          '2': { text: '该员工已符合导师资格要求', img: require('packages/theme-grace/img/icon-green.png') },
          '3': { text: '所选导师与新员工存在以下匹配风险，请谨慎选择', img: require('packages/theme-grace/img/icon-yellow.png') },
          '4': { text: '您所选择导师人选为高管群体，请您再次确认人员准确性', img: require('packages/theme-grace/img/icon-red.png') }
        },
        riskReason: '',
        checkRiskLoading: false, // 选择导师时--校验导师风险加上loading
        searchEmptyText: '' // 搜索为空时的提示语
      }
    },
    watch: {
      data: {
        handler(val) {
          if (val && val.length) {
            this.selectedData = val
            this.selectedCount = val.length
          }
        },
        immediate: true
      },
      selectedNew: {
        handler(val) {
          if (val && val.length) {
            this.selectedData = val
            this.selectedCount = val.length
            // 回显树里面的勾选
            const tree = this.$refs.tree
            if (tree) {
              this.traverse(tree)
            }
          }
        },
        immediate: true
      },
      opened(val) {
        if (val) {
          this.$nextTick(() => {
            // this.$refs.input.focus()
          })
        }
      },
      selectedData(val) {
        this.selectedCount = val.length
        this.$emit('input', val)
        this.$emit('change', val)
      },
      selectedCount(val) {
        this.$emit('selected-count', val)
      },
      riskResultProp: {
        handler(val) {
          this.riskResult = val || {}
          this.riskReason = this.riskResult.entry_reason || ''
        },
        deep: true,
        immediate: true
      }
    },
    computed: {
      riskClass() {
        let riskClass = ''
        if (['-1', -1, 4, '4'].includes(this.riskResult.check_result)) {
          riskClass = 'risk-title-red'
        } else if (['3', 3].includes(this.riskResult.check_result)) {
          riskClass = 'risk-title-yellow'
        } else {
          riskClass = 'risk-title-green'
        }
        return riskClass
      },
      // 显示风险提示标题
      showRiskTitle() {
        return this.riskMap[this.riskResult.check_result] && !this.talentNoShow && !this.otherNoShow
      },
      // 人才透视系统（本人修改时）不显示合格和风险
      talentNoShow() {
        return this.talentAndSelf && [1, 2, 3, '1', '2', '3'].includes(this.riskResult.check_result)
      },
      otherNoShow() {
        return !this.talentAndSelf && [4, '4'].includes(this.riskResult.check_result)
      },
      // 不满足门槛资格
      noThreshold() {
        return this.riskResult.threshold_risk_detail && this.riskResult.threshold_risk_detail.length
      },
      // 不满足认证要求
      noCert() {
        return this.riskResult.cert_risk_detail && this.riskResult.cert_risk_detail.length
      },
      // 辅导多位新员工
      moreStudents() {
        return this.riskResult.student_names && this.riskResult.student_names.length
      },
      // 导师与新员工部门不同
      isDiffDepart() {
        try {
          // return this.riskResult.tutor_dept_id && this.riskResult.student_dept_id && this.riskResult.tutor_dept_id !== this.riskResult.student_dept_id
          return ![undefined, null].includes(this.riskResult.tutor_dept_id) && this.riskResult.tutor_dept_id !== this.riskResult.student_dept_id
        } catch (error) {
          return false
        }
      },
      // 导师与新员工工作地不同
      isDiffWorkPlace() {
        try {
          // return this.riskResult.tutor_work_place_id && this.workPlaceInfo.place_id && this.riskResult.tutor_work_place_id !== this.workPlaceInfo.place_id
          return ![undefined, null].includes(this.riskResult.tutor_work_place_id) && this.riskResult.tutor_work_place_id !== this.workPlaceInfo.place_id
        } catch (error) {
          return false
        }
      },
      // 选择的导师为高管群体
      // isMoreL4() {
      //   return this.riskResult.manage_level_id && this.riskResult.manage_level_id >= 70
      // },
      canConfirm() { // 确认按钮是否可用
        let result = true
        if ([-1, '-1'].includes(this.riskResult.check_result)) { // 导师被禁用
          result = false
        } else if (!this.talentAndSelf && this.noThreshold && !this.riskReason.trim()) { // 没有填写匹配原因
          result = false
        } else if (this.riskResult.getRiskFail && this.failNum < 3) { // 风险判断失败3次以下（连续失败）
          result = false
        } 
        return result
      },
      selectedItemsText() {
        return this.selectedText.replace('$count', this.selectedCount)
      },
      avatarUrl() {
        return require('packages/theme-grace/img/avatar.gif')
      },
      suffixIcon() {
        let icon = this.multiple ? 'el-input__icon el-icon-circle-close' : ''
        if (!this.getSelected().length) {
          icon = ''
        }
        return icon
      },
      noList() {
        const list = ['占位符']
        if (this.noThreshold) {
          list.push('threshold')
        }
        if (this.noCert) {
          list.push('cert')
        }
        if (this.moreStudents) {
          list.push('student')
        }
        if (this.isDiffDepart) {
          list.push('depart')
        }
        if (this.isDiffWorkPlace) {
          list.push('workPlace')
        }
        return list
      },
      platform() {
        return this.getPlatform() || ''
      },
      typeId() {
        return this.getTypeId() || ''
      },
      operatorRightMap() {
        return this.getOperatorRightMap() || []
      },
      requireWrongNum() { // 权限接口错误的次数 3次后不提示刷新 身份默认是"运营人员"
        return this.getWrongNum() || 0
      },
      wrongLoaading() { // 权限接口报错时的loading
        return this.getWrongLoaading() || false
      }
    },
    methods: {
      textEllipsis,
      toAuthenticationPage(course_url) {
        course_url && window.open(course_url)
      },
      handleValidor(value, num) {
        if (value) {
          const china = value.match(/[\u4e00-\u9fa5]/g)
          const zhCount = china && china.join('').length
          const enCount = Math.ceil((value.length - zhCount) / 2)
          const total = zhCount + enCount
          if (total > num) {
            this.riskReason = value.slice(0, -1)
          }
          return total || 0
        }
        return 0
      },
      calcStrNum(value) {
        if (value) {
          const china = value.match(/[\u4e00-\u9fa5]/g)
          const zhCount = china && china.join('').length
          const enCount = Math.ceil((value.length - zhCount) / 2)
          const total = zhCount + enCount
          return total || 0
        }
        return 0
      },
      handleSelect(data) {
        if (this.selectedData && this.selectedData.length) {
          if (data.StaffID !== this.selectedData[0].StaffID) {
            this.handleClear()
          } else {
            this.queryText = ''
            Toast(this.$st('sdc.selector.exist'))
            return
          }
        }
        this.initOtherData()
        // 发请求校验风险
        if (data.StaffID) {
          this.checkRiskLoading = true
          let riskResult = {}
          const beforeObj = {
            student_staff_id: this.studentStaffId || '', // 候选人的员工id
            student_resume_id: this.student_resume_id || '', // 候选人的简历id
            student_org_id: String(this.deptId) === '0' ? 0 : this.deptId || '', // 候选人的最小组织id
            student_work_place_id: this.workPlaceInfo.place_id || '', // 学生的工作地id
            tutor_staff_id: data.StaffID || '', // 导师id
            // staff_id: this.operatorId || '', // 当前操作的员工
            type_id: this.typeId || '', // 员工身份信息
            t: Date.now()
          }
          const params = {
            condition: this.encrypteParams(beforeObj)
          }
          http.get(this.getBaseUrl() + `/api/tutor/user/riskCheck`, { params, timeout: 3000 }).then((res) => {
            if (!res) {
              riskResult = { getRiskFail: true }
              return
            }
            const obj = { ...res, getRiskFail: false, entry_reason: '' }
            if (!this.specialPlatform.includes(String(this.platform))) {
              obj.entry_reason = res.entry_reason || ''
            }
            riskResult = obj
          }).catch(err => {
            console.log('err~~~~: ', err)
            riskResult = { getRiskFail: true }
          }).finally(() => {
            this.riskReason = riskResult.entry_reason || ''
            this.riskResult = riskResult
            this.updateSelectedData([data], 'add', data)
            this.queryText = ''
            // 回显树里面的勾选
            const tree = this.$refs.tree
            if (tree) {
              this.traverse(tree)
            }
            this.checkRiskLoading = false
          })
        }
      },
      // 递归遍历节点树
      traverse(node, live = true) {
        let childNodes = null
        if (live) {
          childNodes = node.root ? node.root.childNodes : node.childNodes
        } else {
          childNodes = node
        }
        if (!childNodes || !childNodes.length) return
        childNodes.forEach((item) => {
          const data = item.data
          if (item.isLeaf && data.type === 'staff') {
            item.checked = this.selectedData.some(subItem => String(subItem.StaffID) === String(data.StaffID))
          } else {
            this.traverse(item.childNodes || [], false)
          }
        })
      },
      getSuggetions(query, callback) {
        if (this.isEnter) {
          this.callback([])
          return
        } 
        if (!this.callback) this.callback = callback
        // 人才透视系统没传"操作人身份"参数时
        if ([1, '1'].includes(this.platform) && !this.typeId) {
          Toast(`人才透视系统需要提供'操作人身份'参数`)
          this.callback([])
          return
        }
        // "非人才透视系统"选择了"员工本人"不让选，只有"人才透视系统"才能选"员工本人"
        if (String(this.platform) !== '1' && this.typeId === '3') {
          Toast(`人才透视系统才能传'操作人身份'为'员工本人'`)
          this.callback([])
          return
        }
        // 判断身份权限，没权限不让搜索并提示相应文字
        if (['1', '2'].includes(this.typeId) && this.operatorRightMap.length && this.operatorRightMap[0] === -1) {
          this.callback([])
          return
        }
        if (!DataType.isFunction(this.getDataList) || query === '') {
          const res = []
          callback(res)
        }
        this.getDataList(query, { ...this.queryParams, ...this.getRange() }).then(async res => {
          if (DataType.isEmptyArray(res)) {
            // res = [{ empty: true, message: '暂无人员数据' }]
            res = []
          } else {
            const isNumber = !isNaN(parseFloat(query)) && isFinite(query)
            // 员工选择器筛选名称时
            if (this.mode === 'staff' && !isNumber && res.length > 1) {
              // 需要精准查询一次
              const equalRes = await this.getDataList(query, { ...this.queryParams, ...this.getRange(), count: 1, likeMode: 'equal' })
              if (!DataType.isEmptyArray(equalRes)) {
                const index = res.findIndex(item => item[this.valueKey] === equalRes[0][this.valueKey])
                // 判断是否已存在第一次查询结果中
                if (index > -1) {
                  res.unshift(res.splice(index, 1)[0])
                } else {
                  res.unshift(equalRes[0])
                }
                // 最多只展示十条数据
                if (res.length > 10) {
                  res.length = 10
                }
              }
            }
          }
          // 过滤掉已经被禁用的导师
          res = res.filter(item => !this.getDisabledMentorList().includes(item.StaffID))
          if (this.filterValue && this.filterValue.length > 0) {
            // 存在过滤字段
            res = res.filter(item => {
              return !(this.filterKey in item) || this.filterValue.indexOf(item[this.filterKey]) > -1
            })
          }
          if (DataType.isEmptyArray(res)) {
            // res = [{ empty: true, message: '暂无人员数据' }]
            res = []
            // 搜索为空时的提示语
            if (['4', '5'].includes(this.typeId)) {
              this.searchEmptyText = '暂无人员数据，请检查输入信息是否正确'
            } else if (['3'].includes(this.typeId)) {
              this.searchEmptyText = '请检查输入信息是否正确，当前不支持选择非部门员工担任导师，请通过组织树选择兼岗员工，如仍有疑问可咨询小T(8008)'
            } else {
              this.searchEmptyText = '请检查输入信息是否正确，当前不支持选择管理组织以外的员工担任导师，请通过组织树选择兼岗员工，如仍有疑问可咨询小T(8008)'
            }
          } else {
            this.searchEmptyText = ''
          }
          callback(res)
        }).catch(res => {
          // res = [{ empty: true, message: isNotLogin(res) ? this.$st('sdc.selector.notLogin') : this.$st('sdc.selector.failed') }]
          if (isNotLogin(res)) {
            res = [{ empty: true, message: this.$st('sdc.selector.notLogin') }]
          } else {
            res = []
          }
          // 搜索报错时的提示语
          if (['4', '5'].includes(this.typeId)) {
            this.searchEmptyText = '暂无人员数据，请检查输入信息是否正确'
          } else if (['3'].includes(this.typeId)) {
            this.searchEmptyText = '请检查输入信息是否正确，当前不支持选择非部门员工担任导师，请通过组织树选择兼岗员工，如仍有疑问可咨询小T(8008)'
          } else {
            this.searchEmptyText = '请检查输入信息是否正确，当前不支持选择管理组织以外的员工担任导师，请通过组织树选择兼岗员工，如仍有疑问可咨询小T(8008)'
          }
          callback(res)
        })
      },
      notFound(event) {
        const ele = event.srcElement
        ele.src = this.avatarUrl
        ele.onerror = null
      },
      showModal() {
        // 人才透视系统没传"操作人身份"参数时
        if ([1, '1'].includes(this.platform) && !this.typeId) {
          Toast(`人才透视系统需要提供'操作人身份'参数`)
          return
        }
        // "非人才透视系统"选择了"员工本人"不让选，只有"人才透视系统"才能选"员工本人"
        if (String(this.platform) !== '1' && this.typeId === '3') {
          Toast(`人才透视系统才能传'操作人身份'为'员工本人'`)
          return
        }
        if (this.opened) { // 非首次打开
          if (!this.$refs.tree) { // 防止添加时没组织BP或者管理权限时报错
            this.show()
            return
          }
          const level0Node = this.$refs.tree.root
          if (level0Node.childNodes.length === 0) {
            // 上一次打开加载失败时，需重新加载
            this.loadNode(level0Node, this.resolveTree)
          } else if (hasOwn(this.getRange(), 'unitID') && this.getRange().unitID !== this.lastLimitUnitID) {
            // 用户外部设置unitID时，需重新加载
            level0Node.childNodes = []
            this.loadNode(level0Node, this.resolveTree)
            this.lastLimitUnitID = this.getRange().unitID
          }
        } else { // 首次打开再访问数据
          this.lastLimitUnitID = this.getRange().unitID
          this.opened = true
        }
        this.selectedData = DataUtil.clone(this.data)
        this.selectedCount = this.selectedData.length
        const traverse = (node) => {
          const childNodes = node.root ? node.root.childNodes : node.childNodes
          childNodes.forEach(node => {
            const data = node.data
            if (data.type === this.map.type.staff) {
              const checked = this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])
              if (checked !== node.checked) {
                this.handleCheckItem(node, checked)
              }
            }
            traverse(node)
          })
        }
        if (this.$refs.tree) {
          traverse(this.$refs.tree.root)
        }
        this.show()
      },
      handleCheckItem(node, checked) {
        // const data = node.data
        node.indeterminate = checked === 'half'
        node.checked = checked === true
        const childNodes = node.childNodes.filter(item => item.data.type === this.map.type.staff)
        childNodes.forEach(child => {
          this.handleCheckItem(child, checked)
        })
        const parent = node.parent
        if (parent && parent.level !== 0) {
          this.reInitChecked(parent)
        }
      },
      handleCheckChange(node) {
        this.initOtherData()
        const { id, label, type, isLeaf, indeterminate, ...data } = node.data
        this.handleCheckItem(node, node.checked)
        if (type === this.map.type.staff) {
          if (!this.multiple && node.checked) {
            this.selectedData.forEach(data => {
              this.handleDelete(data)
            })
          }
          // 发请求校验风险
          if (node.checked) {
            this.checkRiskLoading = true
            let riskResult = {}
            const beforeObj = {
              student_staff_id: this.studentStaffId || '', // 候选人的员工id
              student_resume_id: this.student_resume_id || '', // 候选人的简历id
              student_org_id: String(this.deptId) === '0' ? 0 : this.deptId || '', // 候选人的最小组织id
              student_work_place_id: this.workPlaceInfo.place_id || '', // 学生的工作地id
              tutor_staff_id: data.StaffID || '', // 导师id
              // staff_id: this.operatorId || '', // 当前操作的员工
              type_id: this.typeId || '', // 员工身份信息
              t: Date.now()
            }
            const params = {
              condition: this.encrypteParams(beforeObj)
            }
            http.get(this.getBaseUrl() + `/api/tutor/user/riskCheck`, { params, timeout: 3000 }).then((res) => {
              if (!res) {
                riskResult = { getRiskFail: true }
                return
              }
              const obj = { ...res, getRiskFail: false, entry_reason: '' }
              if (!this.specialPlatform.includes(String(this.platform))) {
                obj.entry_reason = res.entry_reason || ''
              }
              riskResult = obj
            }).catch((err) => {
              console.log('err~~~~: ', err)
              riskResult = { getRiskFail: true }
            }).finally(() => {
              this.riskReason = riskResult.entry_reason || ''
              this.riskResult = riskResult
              this.updateSelectedData([data], 'add', node.parent.data)
              this.checkRiskLoading = false
            })
          } else {
            this.updateSelectedData([data], 'delete', node.parent.data)
          }
        } else if (type === this.map.type.unit) {
          this.getChildrenData(data[this.map.unitID], { ...this.queryParams, ...this.getRange() }).then(res => {
            this.updateSelectedData(res, node.checked ? 'add' : 'delete', data)
          })
        }
        this.$emit('check', node)
      },
      handleDelete(data) {
        this.updateSelectedData([data], 'delete')
        const node = this.$refs.tree.getNode(data[this.map.staffID])
        if (node) {
          this.handleCheckItem(node, false)
        }
        this.initOtherData()
      },
      // 刷新
      refresh() {
        if (this.selectedData.length && this.selectedData[0].StaffID) {
          this.checkRiskLoading = true
          const beforeObj = {
            student_staff_id: this.studentStaffId || '', // 候选人的员工id
            student_resume_id: this.student_resume_id || '', // 候选人的简历id
            student_org_id: String(this.deptId) === '0' ? 0 : this.deptId || '', // 候选人的最小组织id
            student_work_place_id: this.workPlaceInfo.place_id || '', // 学生的工作地id
            tutor_staff_id: this.selectedData[0].StaffID || '', // 导师id
            // staff_id: this.operatorId || '', // 当前操作的员工
            type_id: this.typeId || '', // 员工身份信息
            t: Date.now()
          }
          const params = {
            condition: this.encrypteParams(beforeObj)
          }
          // 回显时报错时的数据 用来区别是不是回显时的错误还是其他情况下的报错
          const firstReason = this.riskResultProp.entry_reason || ''
          http.get(this.getBaseUrl() + `/api/tutor/user/riskCheck`, { params, timeout: 3000 }).then((res) => {
            if (res) {
              const obj = { ...res, getRiskFail: false, entry_reason: '' }
              if (firstReason) {
                obj.entry_reason = firstReason || ''
              } else if (!this.specialPlatform.includes(String(this.platform))) {
                obj.entry_reason = res.entry_reason || ''
              }
              this.riskResult = obj
              this.$emit('changeFailNum', 1)
            } else {
              this.$emit('changeFailNum', this.failNum + 1)
            }
          }).catch(err => {
            console.log('err~~~: ', err)
            this.$emit('changeFailNum', this.failNum + 1)
          }).finally(() => {
            this.riskReason = this.riskResult.entry_reason || ''
            setTimeout(() => {
              this.checkRiskLoading = false
            }, 150)
          })
        }
      },
      handleConfirm() {
        if (!this.talentAndSelf && this.noThreshold && !this.riskReason.trim()) {
          // Toast('请输入匹配原因', 2000)
          this.$message.warning('请输入匹配原因', 2000)
          return
        } else if (this.handleValidor(this.riskReason, 500) > 500) {
          this.$message.warning('匹配原因最多可输入500中文字符', 2000)
          return
        }
        const data = DataUtil.clone(this.selectedData)
        this.change(data)
        this.$emit('getReason', this.riskReason)
        this.riskResult.entry_reason = this.riskReason
        this.hide()
        this.setRiskResult(this.riskResult)
      },
      cancel() {
        this.hide()
        this.initOtherData()
      },
      closeDialog() {
        this.initOtherData()
      },
      showDialog() {
        this.riskResult = this.riskResultProp || {}
        this.riskReason = this.riskResult.entry_reason || ''
        this.queryText = ''
      },
      // 初始化校验结果和失败次数数据
      initOtherData() {
        this.riskReason = ''
        this.riskResult = {}
        this.$emit('changeFailNum', 1)
      },
      reInitChecked(node) {
        const { all, none, half } = this.getChildState(node.childNodes.filter(item => item.data.type === this.map.type.staff)) // 获取子节点的checked情况
        if (all) {
          node.checked = true
          node.indeterminate = false
        } else if (half) {
          node.checked = false
          node.indeterminate = true
        } else if (none) {
          node.checked = false
          node.indeterminate = false
        }
        const parent = node.parent
        if (parent && parent.level !== 0) {
          this.reInitChecked(parent)
        }
      },
      getChildState(nodes) {
        let all = nodes.length > 0
        let none = true
        nodes.forEach(node => {
          if (node.checked !== true || node.indeterminate) {
            all = false
          }
          if (node.checked !== false || node.indeterminate) {
            none = false
          }
        })
        return { all, none, half: !all && !none }
      },
      updateSelectedData(list, type, node) {
        if (type === 'add') {
          list.forEach(data => {
            if (!this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])) {
              data[this.map.unitID] = node[this.map.unitID]
              data[this.map.unitName] = node[this.map.unitName]
              data[this.map.unitFullName] = node[this.map.unitFullName]
              this.selectedData.push(data)
              this.selectedCount++
            }
          })
        } else if (type === 'delete') {
          list.forEach(data => {
            const index = this.selectedData.findIndex(item => item[this.nodeKey] === data[this.nodeKey])
            if (index !== -1) {
              this.selectedData.splice(index, 1)
              this.selectedCount--
            }
          })
        }
      },
      loadNode(node, resolve) {
        if (!this.resolveTree) {
          this.resolveTree = resolve
        }
        // 判断组件是否有传unitID进来，有传的话需要兼容处理，原先Number类型，现在Array类型，需要把Number转Array
        const baseId = this.getRange().unitID ? (!Array.isArray(this.getRange().unitID) ? [this.getRange().unitID] : this.getRange().unitID) : 0
        const id = node.level === 0 ? baseId : node.data[this.map.unitID]
        const isCache = (!hasOwn(this.getRange(), 'unitID') && node.level === 0) // 由外部给定顶层组织，和点击node时不读取缓存
        node.level === 0 && (this.loading = true)
        this.getTreeData(id, { ...this.queryParams, isCache, ...this.getRange() }).then(res => {
          let staffList = res[this.map.type.staff] || []
          let unitList = res[this.map.type.unit] || []
          // 添加tree节点需要的一些属性
          staffList = staffList.map(data => ({
            ...data,
            id: data[this.nodeKey], // 用于设置checkbox的选中状态
            label: data[this.map.staffName],
            isLeaf: true,
            type: this.map.type.staff,
            indeterminate: false
          }))
          staffList = staffList.filter(item => !this.getDisabledMentorList().includes(item.StaffID))
          unitList = unitList.map(data => ({
            ...data,
            id: 'unitID_' + data[this.map.unitID], // 用于设置checkbox的选中状态
            label: data[this.map.unitName],
            isLeaf: false,
            type: this.map.type.unit,
            indeterminate: false
          }))
          const children = staffList.concat(unitList)
          if (this.loading) {
            this.loading = false
          }
          resolve(children)
          if (this.$refs.tree) {
            const checkedNodeKeys = staffList.filter(data => this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])).map(item => item[this.nodeKey])
            this.$refs.tree.setCheckedKeys([...checkedNodeKeys, ...this.$refs.tree.getCheckedKeys()])
          }
          this.reInitChecked(node)
          if (node.level === 0 && this.defaultExpandedKeys && this.defaultExpandedKeys.length) {
            node.childNodes.forEach(item => {
              if (this.defaultExpandedKeys.includes(item.data[this.map.unitID])) {
                item.expand()
              }
            })
          }
        }).catch(res => {
          resolve([])
          Toast(isNotLogin(res) ? this.$st('sdc.selector.notLogin') : this.$st('sdc.selector.failed'), 2000)
          if (this.loading) {
            this.loading = false
          }
          node.expanded = false
          node.loaded = false
          node.isLeaf = false
        })
      },
      handleClear() {
        this.selectedData.length = 0
        this.reloadKey++
        this.selectedCount = 0
        this.$refs.tree.setCheckedKeys([])
      },
      nodeClick(data, node) {
        if (data.type === this.map.type.staff) {
          node.checked = !node.checked
          this.handleCheckChange(node)
        }
      },
      changeRiskReason() {
        setTimeout(() => {
          const parentBox = this.$refs.riskDesc
          parentBox.scrollTop = parentBox.scrollHeight
        }, 0)
      },
      handleblur() {
        setTimeout(() => {
          this.focus = false
          this.queryText = ''
          this.searchEmptyText = ''
        }, 300)
      }
    },
    components: {
      sdcModal
    }
  }
</script>
<style lang="less">
.sdc-mentor-selector-popover-customer-item.el-popover--plain {
  background: #333 !important;
  color: #fff !important;
  font-size: 12px !important;
  padding: 10px !important;
  border-color: #333 !important;
  max-width: 400px;
  .popper__arrow {
    border-top-color: #333 !important;
    &::after {
      border-top-color: #333 !important;
    }
  }
}
</style>
<style lang="less" scoped>
.selector-modal-customer {
  .sdc-modal {
    .modal-header {
      .header-main {
        display: flex;
        align-items: center;
        .header-text {
          color: #333333;
          font-size: 16px;
          font-weight: 550;
          margin-right: 20px;
        }
        .header-warm {
          color: #ed7b2f;
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
    .modal-body {
      .left-side {
        margin: 0;
        padding-left: 14px;
        border-right: 1px solid #F2F2F2;
        overflow: hidden;
        width: 348px;
        height: 474px;
        .search-main {
          position: relative;
          display: flex;
          align-items: center;
          height: 35px;
          margin: 18px 17px 10px 6px;
          .inline-input-left-side {
            width: 100%;
            height: 100%;
            /deep/.el-input {
              font-size: 14px;
              &>input::placeholder {
                font-size: 14px;
              }
            }
          }
          .empty-trip-customer {
            position: absolute;
            top: 36px;
            left: 0;
            padding: 13px 16px;
            width: 100%;
            color: #00000099;
            font-size: 14px;
            line-height: 22px;
            border-radius: 6px;
            border: 0.5px solid #DCDCDC;
            box-shadow: 0 6px 30px 5px #0000000d, 0 16px 24px 2px #0000000a, 0 8px 10px -5px #00000014;
            background: #fff;
            z-index: 999;
          }
        }
        .tree-list {
          height: calc(100% - 70px);
          overflow: auto;
          .tree-node-text {
            color: #333333;
          }
          /deep/.el-tree-node__content {
            height: 40px;
          }
          /deep/.el-checkbox {
            margin-right: 17px;
          }
        }
      }
      .right-side {
        width: 400px;
        height: 474px;
        margin: 0px;
        padding: 12px 0 0 0;
        .selected-risk-main {
          padding-left: 13px;
          min-height: 398px;
        }
        .selected-list {
          padding-right: 18px;
          margin-bottom: 11px;
          height: auto;
          .list-item-name {
            color: #333;
          }
        }
        .risk-outcomm-main {
          height: 345px;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          .risk-outcomm-title {
            display: flex;
            margin-right: 20px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0;
            .risk-icon {
              width: 20px;
              height: 20px;
              margin-right: 8px;
            }
            .risk-text {
              color: #000000e6;
              font-size: 14px;
              line-height: 22px;
            }
          }
          .risk-title-green {
            background-color: #C6F3D7;
          }
          .risk-title-red {
            background-color: #FFD8D2;
          }
          .risk-title-yellow {
            background-color: #FFD9C2;
          }
          .risk-desc {
            margin: 12px 4px 0 0;
            padding-right: 16px;
            flex: 1;
            overflow: auto;
            .risk-desc-item {
              margin-bottom: 12px;
              .risk-desc-title {
                color: #000000e6;
                font-size: 12px;
                line-height: 20px;
                display: flex;
                .q-no {
                  margin-right: 3px;
                }
                .empty-text {
                  color: #d54941;
                }
                .risk-desc-title-r {
                  color: #666666; 
                }
              }
              .risk-desc-subitem {
                display: flex;
                flex-wrap: wrap;
                .risk-desc-subitem-text {
                  margin: 8px 8px 0 0;
                  overflow: hidden;
                  .risk-tooltip-inner {
                    display: flex;
                    align-items: center;
                    padding: 2px 8px;
                    line-height: 20px;
                    font-size: 12px;
                    color: #d54941;
                    border-radius: 3px;
                    background: #FFF0ED;
                    .risk-item-icon {
                      width: 14px;
                      margin-right: 4px;
                    }
                    .risk-item-text {
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      display: inline-block;
                    }
                    .risk-2-link {
                      cursor: pointer;
                      text-decoration: underline;
                      text-decoration-skip-ink: auto;
                      text-underline-offset: 0.25em;
                    }
                  }
                }
              }

            }
            .risk-reason {
              position: relative;
              color: #00000099;
              font-size: 12px;
              line-height: 22px;
              margin-bottom: 10px;
              .risk-reason-input {
                margin-top: 8px;
                width: 100%;
                font-size: 12px;
                /deep/.el-textarea__inner {
                  padding-bottom: 20px;
                  &::placeholder {
                    font-size: 12px;
                  }
                }
              }
              .risk-char-count {  
                position: absolute;  
                bottom: 5px;
                right: 10px;
                font-size: 10px;
                padding: 0 2px;
                line-height: 16px;
                height: 16px;
                color: #f56c6c;
                background: #fff;
              }
            }
          }
          .risk-fail {
            margin-top: 48px;
            text-align: center;
            .risk-fail-img {
              width: 160px;
              height: 160px;
            }
            .risk-fail-title {
              color: #00000099;
              text-align: center;
              font-size: 14px;
              line-height: 22px;
            }
            .risk-refresh-button {
              color: #0052D9;
              line-height: 22px;
              border: none;
            }
          }
        }
        .modal-buttons {
          margin-top: 0;
          padding: 16px 16px 0 0;
          border-top: 1px solid #eee;
          .el-button--small {
            font-size: 14px;
          }
        }
      }
      .no-power-content {
        text-align: center;
        height: 474px;
        .no-power-img {
          width: 239px;
          height: 148px;
          margin: 100px auto 16px;
        }
        .no-power-text {
          color: #000000e6;
          font-size: 18px;
          font-weight: 600;
          line-height: 25px;
          .refresh-btn {
            font-size: 18px;
            font-weight: 600;
            color: #0052D9;
            border: none;
          }
        }
      }
    }
  }
}
</style>
