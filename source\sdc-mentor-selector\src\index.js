/* Automatically generated by './build/bin/build-entry.js' */

import Loading from '../packages/loading'
import Toast from '../packages/toast'
import MentorSelector from '../packages/mentor-selector'
import locale from 'sdc-webui/src/locale'
import EventBus from 'sdc-webui/src/utils/bus'

const components = [
  MentorSelector
]

const install = (Vue, opts = {}) => {
  locale.use(opts.locale)
  locale.i18n(opts.i18n)

  components.forEach(component => {
    Vue.component(component.name, component)
  })

  Vue.prototype.$bus = new EventBus()
  Vue.prototype.$sdc = {}
  Vue.prototype.$sdc.loading = Loading
  Vue.prototype.$sdc.loading.hide = Loading.hide
  Vue.prototype.$sdc.toast = Toast
  Vue.prototype.$sdc.alert = Alert
  Vue.prototype.$sdc.prompt = Prompt
  Vue.prototype.$sdc.prompt.hide = Prompt.hide
}

if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

export default {
  version: '0.0.81',
  locale: locale.use,
  i18n: locale.i18n,
  install,
  Loading,
  Toast,
  MentorSelector
}
